/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Değişiklik yapılacak hedefin hedefNo ve hedefTip bilgileri
 */
@JsonPropertyOrder({
  Hedef.JSON_PROPERTY_HEDEF_NO,
  Hedef.JSON_PROPERTY_HEDEF_TIP
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class Hedef {
  public static final String JSON_PROPERTY_HEDEF_NO = "hedefNo";
  private String hedefNo;

  /**
   * Gets or Sets hedefTip
   */
  public enum HedefTipEnum {
    _10("10"),
    
    _20("20"),
    
    _30("30"),
    
    _40("40"),
    
    _41("41"),
    
    _42("42"),
    
    _43("43"),
    
    _44("44"),
    
    _50("50"),
    
    _51("51"),
    
    _52("52"),
    
    _53("53"),
    
    _54("54"),
    
    _55("55"),
    
    _56("56"),
    
    _60("60"),
    
    _70("70"),
    
    _71("71"),
    
    _80("80"),
    
    _81("81"),
    
    _82("82"),
    
    _83("83"),
    
    _90("90"),
    
    _91("91"),
    
    _92("92"),
    
    _99("99"),
    
    _200("200"),
    
    _210("210"),
    
    _201("201"),
    
    _211("211");

    private String value;

    HedefTipEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static HedefTipEnum fromValue(String value) {
      for (HedefTipEnum b : HedefTipEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_HEDEF_TIP = "hedefTip";
  private HedefTipEnum hedefTip;

  public Hedef() {
  }

  public Hedef hedefNo(String hedefNo) {
    
    this.hedefNo = hedefNo;
    return this;
  }

   /**
   * Get hedefNo
   * @return hedefNo
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_HEDEF_NO)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getHedefNo() {
    return hedefNo;
  }


  @JsonProperty(JSON_PROPERTY_HEDEF_NO)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setHedefNo(String hedefNo) {
    this.hedefNo = hedefNo;
  }


  public Hedef hedefTip(HedefTipEnum hedefTip) {
    
    this.hedefTip = hedefTip;
    return this;
  }

   /**
   * Get hedefTip
   * @return hedefTip
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_HEDEF_TIP)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public HedefTipEnum getHedefTip() {
    return hedefTip;
  }


  @JsonProperty(JSON_PROPERTY_HEDEF_TIP)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setHedefTip(HedefTipEnum hedefTip) {
    this.hedefTip = hedefTip;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Hedef hedef = (Hedef) o;
    return Objects.equals(this.hedefNo, hedef.hedefNo) &&
        Objects.equals(this.hedefTip, hedef.hedefTip);
  }

  @Override
  public int hashCode() {
    return Objects.hash(hedefNo, hedefTip);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Hedef {\n");
    sb.append("    hedefNo: ").append(toIndentedString(hedefNo)).append("\n");
    sb.append("    hedefTip: ").append(toIndentedString(hedefTip)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

