package iym.makos.handler;

import iym.common.model.entity.iym.MahkemeKararTalep;
import iym.db.jpa.dao.*;
import iym.makos.dto.id.GenelEvrakRequest;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Optional;

@Component
@Slf4j
public class GenelEvrakDBSaveHandler extends MahkemeKararRequestDbSaveHandlerBase<GenelEvrakRequest> {

    private final MahkemeKararRepo mahkemeKararRepo;
    private MahkemeKararTalepRepo mahkemeKararTalepRepo;
    private DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo;
    private HedeflerTalepRepo hedeflerTalepRepo;
    private HedeflerRepo hedeflerRepo;
    private HedeflerAidiyatTalepRepo hedeflerAidiyatTalepRepo;
    private KararRequestMapper kararRequestMapper;
    private final MahkemeAidiyatTalepRepo mahkemeAidiyatTalepRepo;
    private final MahkemeSuclarTalepRepo mahkemeSuclarTalepRepo;

    @Autowired
    public GenelEvrakDBSaveHandler(MahkemeKararRepo mahkemeKararRepo
            , MahkemeKararTalepRepo mahkemeKararTalepRepo
            , DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo
            , HedeflerRepo hedeflerRepo
            , HedeflerTalepRepo hedeflerTalepRepo
            , HedeflerAidiyatTalepRepo hedeflerAidiyatTalepRepo
            , KararRequestMapper kararRequestMapper
            , MahkemeAidiyatTalepRepo mahkemeAidiyatTalepRepo
            , MahkemeSuclarTalepRepo mahkemeSuclarTalepRepo) {
        this.mahkemeKararRepo = mahkemeKararRepo;
        this.mahkemeKararTalepRepo = mahkemeKararTalepRepo;
        this.hedeflerRepo = hedeflerRepo;
        this.hedeflerTalepRepo = hedeflerTalepRepo;
        this.hedeflerAidiyatTalepRepo = hedeflerAidiyatTalepRepo;
        this.kararRequestMapper = kararRequestMapper;
        this.mahkemeAidiyatTalepRepo = mahkemeAidiyatTalepRepo;
        this.mahkemeSuclarTalepRepo = mahkemeSuclarTalepRepo;
    }

    @Override
    @Transactional
    public Long kaydet(GenelEvrakRequest request, Date kayitTarihi, Long kullaniciId) throws Exception {
        try {
            Long mahkemeKararTalepId = mahkemeKararRequestCommonDbSaver.handleDbSave(request, kayitTarihi, kullaniciId);

            Optional<MahkemeKararTalep> mahkemeKararTalepOpt = mahkemeKararTalepRepo.findById(mahkemeKararTalepId);
            if (mahkemeKararTalepOpt.isEmpty()) {
                throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARARTALEP_KAYDETMEHATASI);
            }
            return mahkemeKararTalepId;
        } catch (Exception ex) {
            log.error("GenelEvrak handleDbSave failed. id:{}, evrakNo:{}", request.getId(), request.getEvrakDetay().getEvrakNo(), ex);
            throw new RuntimeException(ex);
        }
    }

}

