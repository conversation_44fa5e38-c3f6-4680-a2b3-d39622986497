package iym.makos.api.client.gen.api;

import iym.makos.api.client.gen.handler.ApiClient;

import iym.makos.api.client.gen.model.ActivateUserResponse;
import iym.makos.api.client.gen.model.AddUserRequest;
import iym.makos.api.client.gen.model.AddUserResponse;
import iym.makos.api.client.gen.model.DeactivateUserResponse;
import iym.makos.api.client.gen.model.DeleteUserRequest;
import iym.makos.api.client.gen.model.DeleteUserResponse;
import iym.makos.api.client.gen.model.GetUserByIdResponse;
import iym.makos.api.client.gen.model.GetUserResponse;
import iym.makos.api.client.gen.model.UpdateUserRequest;
import iym.makos.api.client.gen.model.UpdateUserResponse;
import iym.makos.api.client.gen.model.UsernameRequest;
import iym.makos.api.client.gen.model.UsersListResponse;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class UserControllerApi {
    private ApiClient apiClient;

    public UserControllerApi() {
        this(new ApiClient());
    }

    public UserControllerApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param usernameRequest  (required)
     * @return ActivateUserResponse
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ActivateUserResponse activateUser(UsernameRequest usernameRequest) throws RestClientException {
        return activateUserWithHttpInfo(usernameRequest).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param usernameRequest  (required)
     * @return ResponseEntity&lt;ActivateUserResponse&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ActivateUserResponse> activateUserWithHttpInfo(UsernameRequest usernameRequest) throws RestClientException {
        Object localVarPostBody = usernameRequest;
        
        // verify the required parameter 'usernameRequest' is set
        if (usernameRequest == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'usernameRequest' when calling activateUser");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "*/*"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "BasicAuth" };

        ParameterizedTypeReference<ActivateUserResponse> localReturnType = new ParameterizedTypeReference<ActivateUserResponse>() {};
        return apiClient.invokeAPI("/user/activate", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param addUserRequest  (required)
     * @return AddUserResponse
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public AddUserResponse addUser(AddUserRequest addUserRequest) throws RestClientException {
        return addUserWithHttpInfo(addUserRequest).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param addUserRequest  (required)
     * @return ResponseEntity&lt;AddUserResponse&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<AddUserResponse> addUserWithHttpInfo(AddUserRequest addUserRequest) throws RestClientException {
        Object localVarPostBody = addUserRequest;
        
        // verify the required parameter 'addUserRequest' is set
        if (addUserRequest == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'addUserRequest' when calling addUser");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "*/*"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "BasicAuth" };

        ParameterizedTypeReference<AddUserResponse> localReturnType = new ParameterizedTypeReference<AddUserResponse>() {};
        return apiClient.invokeAPI("/user/add", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param usernameRequest  (required)
     * @return DeactivateUserResponse
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public DeactivateUserResponse deactivateUser(UsernameRequest usernameRequest) throws RestClientException {
        return deactivateUserWithHttpInfo(usernameRequest).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param usernameRequest  (required)
     * @return ResponseEntity&lt;DeactivateUserResponse&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<DeactivateUserResponse> deactivateUserWithHttpInfo(UsernameRequest usernameRequest) throws RestClientException {
        Object localVarPostBody = usernameRequest;
        
        // verify the required parameter 'usernameRequest' is set
        if (usernameRequest == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'usernameRequest' when calling deactivateUser");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "*/*"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "BasicAuth" };

        ParameterizedTypeReference<DeactivateUserResponse> localReturnType = new ParameterizedTypeReference<DeactivateUserResponse>() {};
        return apiClient.invokeAPI("/user/deactivate", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param deleteUserRequest  (required)
     * @return DeleteUserResponse
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public DeleteUserResponse deleteUser(DeleteUserRequest deleteUserRequest) throws RestClientException {
        return deleteUserWithHttpInfo(deleteUserRequest).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param deleteUserRequest  (required)
     * @return ResponseEntity&lt;DeleteUserResponse&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<DeleteUserResponse> deleteUserWithHttpInfo(DeleteUserRequest deleteUserRequest) throws RestClientException {
        Object localVarPostBody = deleteUserRequest;
        
        // verify the required parameter 'deleteUserRequest' is set
        if (deleteUserRequest == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'deleteUserRequest' when calling deleteUser");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "*/*"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "BasicAuth" };

        ParameterizedTypeReference<DeleteUserResponse> localReturnType = new ParameterizedTypeReference<DeleteUserResponse>() {};
        return apiClient.invokeAPI("/user/delete", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @return UsersListResponse
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public UsersListResponse getAllUsers() throws RestClientException {
        return getAllUsersWithHttpInfo().getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @return ResponseEntity&lt;UsersListResponse&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<UsersListResponse> getAllUsersWithHttpInfo() throws RestClientException {
        Object localVarPostBody = null;
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "*/*"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "BasicAuth" };

        ParameterizedTypeReference<UsersListResponse> localReturnType = new ParameterizedTypeReference<UsersListResponse>() {};
        return apiClient.invokeAPI("/user/getAllUsers", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param username  (required)
     * @return GetUserResponse
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public GetUserResponse getUser(String username) throws RestClientException {
        return getUserWithHttpInfo(username).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param username  (required)
     * @return ResponseEntity&lt;GetUserResponse&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<GetUserResponse> getUserWithHttpInfo(String username) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'username' is set
        if (username == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'username' when calling getUser");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "username", username));


        final String[] localVarAccepts = { 
            "*/*"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "BasicAuth" };

        ParameterizedTypeReference<GetUserResponse> localReturnType = new ParameterizedTypeReference<GetUserResponse>() {};
        return apiClient.invokeAPI("/user", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param id  (required)
     * @return GetUserByIdResponse
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public GetUserByIdResponse getUserById(Long id) throws RestClientException {
        return getUserByIdWithHttpInfo(id).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param id  (required)
     * @return ResponseEntity&lt;GetUserByIdResponse&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<GetUserByIdResponse> getUserByIdWithHttpInfo(Long id) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'id' is set
        if (id == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'id' when calling getUserById");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("id", id);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "*/*"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "BasicAuth" };

        ParameterizedTypeReference<GetUserByIdResponse> localReturnType = new ParameterizedTypeReference<GetUserByIdResponse>() {};
        return apiClient.invokeAPI("/user/{id}", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @return UsersListResponse
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public UsersListResponse getUsersForAdmin() throws RestClientException {
        return getUsersForAdminWithHttpInfo().getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @return ResponseEntity&lt;UsersListResponse&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<UsersListResponse> getUsersForAdminWithHttpInfo() throws RestClientException {
        Object localVarPostBody = null;
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "*/*"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "BasicAuth" };

        ParameterizedTypeReference<UsersListResponse> localReturnType = new ParameterizedTypeReference<UsersListResponse>() {};
        return apiClient.invokeAPI("/user/getUsersForAdmin", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param updateUserRequest  (required)
     * @return UpdateUserResponse
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public UpdateUserResponse updateUser(UpdateUserRequest updateUserRequest) throws RestClientException {
        return updateUserWithHttpInfo(updateUserRequest).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param updateUserRequest  (required)
     * @return ResponseEntity&lt;UpdateUserResponse&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<UpdateUserResponse> updateUserWithHttpInfo(UpdateUserRequest updateUserRequest) throws RestClientException {
        Object localVarPostBody = updateUserRequest;
        
        // verify the required parameter 'updateUserRequest' is set
        if (updateUserRequest == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'updateUserRequest' when calling updateUser");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "*/*"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "BasicAuth" };

        ParameterizedTypeReference<UpdateUserResponse> localReturnType = new ParameterizedTypeReference<UpdateUserResponse>() {};
        return apiClient.invokeAPI("/user/update", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
}
