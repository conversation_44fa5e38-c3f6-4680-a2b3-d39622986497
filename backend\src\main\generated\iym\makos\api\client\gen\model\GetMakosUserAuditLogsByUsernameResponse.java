/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import iym.makos.api.client.gen.model.ModelApiResponse;
import iym.makos.api.client.gen.model.PageMakosUserAuditLog;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * GetMakosUserAuditLogsByUsernameResponse
 */
@JsonPropertyOrder({
  GetMakosUserAuditLogsByUsernameResponse.JSON_PROPERTY_RESPONSE,
  GetMakosUserAuditLogsByUsernameResponse.JSON_PROPERTY_AUDIT_LOGS
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class GetMakosUserAuditLogsByUsernameResponse {
  public static final String JSON_PROPERTY_RESPONSE = "response";
  private ModelApiResponse response;

  public static final String JSON_PROPERTY_AUDIT_LOGS = "auditLogs";
  private PageMakosUserAuditLog auditLogs;

  public GetMakosUserAuditLogsByUsernameResponse() {
  }

  public GetMakosUserAuditLogsByUsernameResponse response(ModelApiResponse response) {
    
    this.response = response;
    return this;
  }

   /**
   * Get response
   * @return response
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_RESPONSE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public ModelApiResponse getResponse() {
    return response;
  }


  @JsonProperty(JSON_PROPERTY_RESPONSE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setResponse(ModelApiResponse response) {
    this.response = response;
  }


  public GetMakosUserAuditLogsByUsernameResponse auditLogs(PageMakosUserAuditLog auditLogs) {
    
    this.auditLogs = auditLogs;
    return this;
  }

   /**
   * Get auditLogs
   * @return auditLogs
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_AUDIT_LOGS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public PageMakosUserAuditLog getAuditLogs() {
    return auditLogs;
  }


  @JsonProperty(JSON_PROPERTY_AUDIT_LOGS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAuditLogs(PageMakosUserAuditLog auditLogs) {
    this.auditLogs = auditLogs;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    GetMakosUserAuditLogsByUsernameResponse getMakosUserAuditLogsByUsernameResponse = (GetMakosUserAuditLogsByUsernameResponse) o;
    return Objects.equals(this.response, getMakosUserAuditLogsByUsernameResponse.response) &&
        Objects.equals(this.auditLogs, getMakosUserAuditLogsByUsernameResponse.auditLogs);
  }

  @Override
  public int hashCode() {
    return Objects.hash(response, auditLogs);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class GetMakosUserAuditLogsByUsernameResponse {\n");
    sb.append("    response: ").append(toIndentedString(response)).append("\n");
    sb.append("    auditLogs: ").append(toIndentedString(auditLogs)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

