package iym.makos.talepislem;

import iym.common.model.api.KararTuru;
import iym.common.util.CommonUtils;
import iym.db.jpa.dao.DetayMahkemeKararTalepRepo;
import iym.db.jpa.dao.HedeflerDetayTalepRepo;
import iym.db.jpa.dao.HedeflerTalepRepo;
import iym.makos.dto.talepupdate.MahkemeKararTalepUpdateRequest;
import iym.makos.dto.talepupdate.MahkemeKararTalepUpdateResponse;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@Slf4j
public class IDHedefGuncellemeIsleyici extends IDMahkemeKararIsleyiciBase {

    private final HedeflerDetayTalepRepo hedeflerDetayTalepRepo;
    private final DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo;

    @Autowired
    public IDHedefGuncellemeIsleyici(HedeflerDetayTalepRepo hedeflerDetayTalepRepo
            , DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo
            , HedeflerTalepRepo hedeflerTalepRepo
    ) {
        this.hedeflerDetayTalepRepo = hedeflerDetayTalepRepo;
        this.dMahkemeKararTalepRepo = dMahkemeKararTalepRepo;
    }

    protected MahkemeKararTalepUpdateResponse updateRelatedTables(MahkemeKararTalepUpdateRequest request) {

        try {
            String durum = MahkemeKararTalepIsleyici.toDurum(request.getTalepGuncellemeTuru());
            CommonUtils.safeList(dMahkemeKararTalepRepo.findByMahkemeKararTalepId(request.getMahkemeKararTalepId()))
                    .forEach(dMahkemeKararTalep -> {
                        dMahkemeKararTalep.setDurum(durum);
                        dMahkemeKararTalepRepo.save(dMahkemeKararTalep);

                        CommonUtils.safeList(hedeflerDetayTalepRepo.findByMahkemeKararTalepId(dMahkemeKararTalep.getId()))
                                .forEach(hedeflerDetayTalep ->{
                                    hedeflerDetayTalep.setDurumu(durum);
                                    hedeflerDetayTalepRepo.save(hedeflerDetayTalep);
                                });
                    });

            return MahkemeKararTalepUpdateResponse.builder()
                    .requestId(request.getId())
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .build())
                    .build();

        }catch (Exception ex){
            log.error("MahkemeKararTalepUpdateRequest process failed, requestId:{}, mahkemeKararTalepId:{}", request.getId(), request.getMahkemeKararTalepId(), ex);
            return MahkemeKararTalepUpdateResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Iliskili veritabanı guncelleme hatası")
                            .build())
                    .build();
        }
    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME;
    }

}

