package iym.db.jpa.dao;

import iym.common.model.entity.iym.MahkemeAidiyatDetayTalep;
import iym.common.model.entity.iym.MahkemeSucTipiDetayTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * Repository interface for MahkemeSucTipiDetayTalep entity
 */
@Repository
public interface MahkemeSucTipiDetayTalepRepo extends JpaRepository<MahkemeSucTipiDetayTalep, Long> {

    List<MahkemeSucTipiDetayTalep> findByMahkemeKararDetayTalepId(Long mahkemeKararDetayTalepId);
    

}
