package iym.makos.model.api;

import io.swagger.v3.oas.annotations.media.Schema;
import iym.common.model.api.Hedef;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

import java.util.List;

@Data
@Builder
@Jacksonized
@ToString
@EqualsAndHashCode
public class HedefSonlandirmaKararDetay {

  @NotNull
  @Schema(description = "Değişiklik yapılacak hedefin daha önce gönderilen mahkeme karar bilgileri")
  private MahkemeKararDetay mahkemeKararDetay;

  @NotNull
  @Size(min = 1)
  @Valid
  private List<Hedef> sonlandirmaDetayListesi;

}

