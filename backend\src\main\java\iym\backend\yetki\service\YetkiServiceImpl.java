package iym.backend.postgresql.yetki.service;

import org.springframework.stereotype.Service;
import iym.backend.postgresql.shared.service.BaseServiceImpl;
import iym.backend.postgresql.yetki.dto.YetkiDto;
import iym.backend.postgresql.yetki.entity.Yetki;
import iym.backend.postgresql.yetki.mapper.YetkiMapper;
import iym.backend.postgresql.yetki.repository.YetkiRepository;

import java.util.List;

@Service
public class YetkiServiceImpl extends BaseServiceImpl<Yetki, YetkiDto, Long> implements YetkiService {

    public YetkiServiceImpl(YetkiRepository repository, YetkiMapper mapper) {
        super(repository, mapper);
    }

    @Override
    public List<YetkiDto> getViewRolleri() {
        return mapper.toDtoList(
                repository.findAll().stream()
                        .filter(yetki -> yetki.getAd() != null && yetki.getAd().endsWith("_VIEW"))
                        .toList()
        );
    }
}
