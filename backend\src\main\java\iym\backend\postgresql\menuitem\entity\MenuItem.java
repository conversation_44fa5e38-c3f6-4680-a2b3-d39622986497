package iym.backend.postgresql.menuitem.entity;

import jakarta.persistence.*;
import lombok.*;
import iym.backend.postgresql.menuitemyetki.entity.MenuItemYetki;
import iym.backend.postgresql.shared.entity.BaseEntity;

import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "menu_itemler")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MenuItem extends BaseEntity {

    private String label;
    private String icon;
    private String routerLink;
    private String queryParams;

    private int menuOrder = 0;

    // Self-referencing relationship
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id")
    private MenuItem parent;

    @OneToMany(mappedBy = "parent", cascade = CascadeType.ALL)
    private List<MenuItem> children = new ArrayList<>();

    // MenuItem - Yet<PERSON> il<PERSON>
    @OneToMany(mappedBy = "menuItem", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<MenuItemYetki> menuItemYetkiler = new ArrayList<>();
}
