package iym.backend.postgresql.menuitem.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import iym.backend.postgresql.menuitem.dto.MenuItemDto;
import iym.backend.postgresql.menuitem.service.MenuItemService;

import java.util.List;

@RestController
@RequestMapping("/api/menu-items")
@RequiredArgsConstructor
public class MenuItemController {

    private final MenuItemService service;

    @PostMapping
    public MenuItemDto create(@RequestBody MenuItemDto dto) {
        return service.save(dto);
    }

    @PutMapping("/{id}")
    public MenuItemDto create(@PathVariable Long id,@RequestBody MenuItemDto dto) {

        return service.update(id,dto);
    }

    @GetMapping("/{id}")
    public MenuItemDto get(@PathVariable Long id) {
        return service.findById(id);
    }

    @GetMapping("/menuagacigetir")
    public List<MenuItemDto> menuAgaciGetir() {
        return service.menuAgaciGetir();
    }

    @GetMapping
    public List<MenuItemDto> getAll() {
        return service.findAll();
    }

    @DeleteMapping("/{id}")
    public void delete(@PathVariable Long id) {
        service.delete(id);
    }
}
