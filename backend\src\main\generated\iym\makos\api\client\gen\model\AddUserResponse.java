/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import iym.makos.api.client.gen.model.ModelApiResponse;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * AddUserResponse
 */
@JsonPropertyOrder({
  AddUserResponse.JSON_PROPERTY_RESPONSE,
  AddUserResponse.JSON_PROPERTY_ID
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class AddUserResponse {
  public static final String JSON_PROPERTY_RESPONSE = "response";
  private ModelApiResponse response;

  public static final String JSON_PROPERTY_ID = "id";
  private Long id;

  public AddUserResponse() {
  }

  public AddUserResponse response(ModelApiResponse response) {
    
    this.response = response;
    return this;
  }

   /**
   * Get response
   * @return response
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_RESPONSE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public ModelApiResponse getResponse() {
    return response;
  }


  @JsonProperty(JSON_PROPERTY_RESPONSE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setResponse(ModelApiResponse response) {
    this.response = response;
  }


  public AddUserResponse id(Long id) {
    
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Long getId() {
    return id;
  }


  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setId(Long id) {
    this.id = id;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AddUserResponse addUserResponse = (AddUserResponse) o;
    return Objects.equals(this.response, addUserResponse.response) &&
        Objects.equals(this.id, addUserResponse.id);
  }

  @Override
  public int hashCode() {
    return Objects.hash(response, id);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class AddUserResponse {\n");
    sb.append("    response: ").append(toIndentedString(response)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

