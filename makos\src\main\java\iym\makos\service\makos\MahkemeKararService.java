package iym.makos.service.makos;

import iym.common.model.entity.iym.MahkemeKarar;
import iym.common.service.db.DbMahkemeKararService;
import iym.makos.dto.MahkemeKararDTO;
import iym.makos.mapper.MahkemeKararMapper;
import iym.makos.model.api.MahkemeKararDetay;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.Optional;

/**
 * Service for Iller operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MahkemeKararService {

    private final DbMahkemeKararService dbMahkemeKararService;
    private final MahkemeKararMapper mahkemeKararMapper;

    public MahkemeKararDTO findById(Long id){
        return dbMahkemeKararService.findById(id)
                .map(mahkemeKararMapper::toDto)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme Karar bilgisi bulunamadı"));
    }


    public List<MahkemeKararDTO> findByEvrakId(Long evrakId){
        List<MahkemeKarar> mahkemeKararList = dbMahkemeKararService.findByEvrakId(evrakId);
        return mahkemeKararMapper.toDtoList(mahkemeKararList);
    }

    public  MahkemeKararDTO getMahkemeKararBilgisi(MahkemeKararDetay detay){
        return findBy(detay.getMahkemeIlIlceKodu(), detay.getMahkemeKodu(), detay.getMahkemeKararNo(), detay.getSorusturmaNo());
    }

    public MahkemeKararDTO findBy(
            String mahkemeIlIlceKodu,
            String mahkemeKodu,
            String mahkemeKararNo,
            String sorusturmaNo
    ){
        Optional<MahkemeKarar> mahkemeKararOpt = dbMahkemeKararService.findBy(
                mahkemeIlIlceKodu,
                mahkemeKodu,
                mahkemeKararNo,
                sorusturmaNo);
        return mahkemeKararMapper.toDto(mahkemeKararOpt.orElse(null));
    }


}
