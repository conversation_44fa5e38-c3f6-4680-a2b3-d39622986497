/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import iym.makos.api.client.gen.model.Hedef;
import iym.makos.api.client.gen.model.HedefGuncellemeBilgi;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * HedefGuncellemeDetay
 */
@JsonPropertyOrder({
  HedefGuncellemeDetay.JSON_PROPERTY_HEDEF,
  HedefGuncellemeDetay.JSON_PROPERTY_HEDEF_GUNCELLEME_BILGI_LISTESI
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class HedefGuncellemeDetay {
  public static final String JSON_PROPERTY_HEDEF = "hedef";
  private Hedef hedef;

  public static final String JSON_PROPERTY_HEDEF_GUNCELLEME_BILGI_LISTESI = "hedefGuncellemeBilgiListesi";
  private List<HedefGuncellemeBilgi> hedefGuncellemeBilgiListesi = new ArrayList<>();

  public HedefGuncellemeDetay() {
  }

  public HedefGuncellemeDetay hedef(Hedef hedef) {
    
    this.hedef = hedef;
    return this;
  }

   /**
   * Get hedef
   * @return hedef
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_HEDEF)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Hedef getHedef() {
    return hedef;
  }


  @JsonProperty(JSON_PROPERTY_HEDEF)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setHedef(Hedef hedef) {
    this.hedef = hedef;
  }


  public HedefGuncellemeDetay hedefGuncellemeBilgiListesi(List<HedefGuncellemeBilgi> hedefGuncellemeBilgiListesi) {
    
    this.hedefGuncellemeBilgiListesi = hedefGuncellemeBilgiListesi;
    return this;
  }

  public HedefGuncellemeDetay addHedefGuncellemeBilgiListesiItem(HedefGuncellemeBilgi hedefGuncellemeBilgiListesiItem) {
    if (this.hedefGuncellemeBilgiListesi == null) {
      this.hedefGuncellemeBilgiListesi = new ArrayList<>();
    }
    this.hedefGuncellemeBilgiListesi.add(hedefGuncellemeBilgiListesiItem);
    return this;
  }

   /**
   * Get hedefGuncellemeBilgiListesi
   * @return hedefGuncellemeBilgiListesi
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_HEDEF_GUNCELLEME_BILGI_LISTESI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public List<HedefGuncellemeBilgi> getHedefGuncellemeBilgiListesi() {
    return hedefGuncellemeBilgiListesi;
  }


  @JsonProperty(JSON_PROPERTY_HEDEF_GUNCELLEME_BILGI_LISTESI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setHedefGuncellemeBilgiListesi(List<HedefGuncellemeBilgi> hedefGuncellemeBilgiListesi) {
    this.hedefGuncellemeBilgiListesi = hedefGuncellemeBilgiListesi;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    HedefGuncellemeDetay hedefGuncellemeDetay = (HedefGuncellemeDetay) o;
    return Objects.equals(this.hedef, hedefGuncellemeDetay.hedef) &&
        Objects.equals(this.hedefGuncellemeBilgiListesi, hedefGuncellemeDetay.hedefGuncellemeBilgiListesi);
  }

  @Override
  public int hashCode() {
    return Objects.hash(hedef, hedefGuncellemeBilgiListesi);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class HedefGuncellemeDetay {\n");
    sb.append("    hedef: ").append(toIndentedString(hedef)).append("\n");
    sb.append("    hedefGuncellemeBilgiListesi: ").append(toIndentedString(hedefGuncellemeBilgiListesi)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

