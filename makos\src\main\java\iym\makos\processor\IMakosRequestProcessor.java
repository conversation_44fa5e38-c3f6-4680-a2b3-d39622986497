package iym.makos.processor;

import iym.makos.config.security.UserDetailsImpl;
import iym.makos.model.reqrep.MahkemeKararRequest;
import iym.makos.model.reqrep.MahkemeKararResponse;

public interface IMakosRequestProcessor<T extends MahkemeKararRequest, R extends MahkemeKararResponse> {

    public R process(T request, UserDetailsImpl islemYapanKullanici);

    public Class<T> getRelatedRequestType();

    public Class<R> getRelatedResponseType();

}
