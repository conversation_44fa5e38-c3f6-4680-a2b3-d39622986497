/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * RegisterRequest
 */
@JsonPropertyOrder({
  RegisterRequest.JSON_PROPERTY_USER_NAME,
  RegisterRequest.JSON_PROPERTY_PASSWORD,
  RegisterRequest.JSON_PROPERTY_ROLE,
  RegisterRequest.JSON_PROPERTY_KURUM
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class RegisterRequest {
  public static final String JSON_PROPERTY_USER_NAME = "userName";
  private String userName;

  public static final String JSON_PROPERTY_PASSWORD = "password";
  private String password;

  /**
   * Gets or Sets role
   */
  public enum RoleEnum {
    ADMIN("ROLE_ADMIN"),
    
    QUERY_ADMIN("ROLE_QUERY_ADMIN"),
    
    KURUM_TEMSILCISI("ROLE_KURUM_TEMSILCISI"),
    
    KURUM_KULLANICI("ROLE_KURUM_KULLANICI");

    private String value;

    RoleEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static RoleEnum fromValue(String value) {
      for (RoleEnum b : RoleEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_ROLE = "role";
  private RoleEnum role;

  /**
   * Gets or Sets kurum
   */
  public enum KurumEnum {
    ADLI("ADLI"),
    
    EMNIYET("EMNIYET"),
    
    MIT("MIT"),
    
    JANDARMA("JANDARMA"),
    
    BTK("BTK"),
    
    EMNIYET_SIBER("EMNIYET_SIBER");

    private String value;

    KurumEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static KurumEnum fromValue(String value) {
      for (KurumEnum b : KurumEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_KURUM = "kurum";
  private KurumEnum kurum;

  public RegisterRequest() {
  }

  public RegisterRequest userName(String userName) {
    
    this.userName = userName;
    return this;
  }

   /**
   * Get userName
   * @return userName
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_USER_NAME)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getUserName() {
    return userName;
  }


  @JsonProperty(JSON_PROPERTY_USER_NAME)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setUserName(String userName) {
    this.userName = userName;
  }


  public RegisterRequest password(String password) {
    
    this.password = password;
    return this;
  }

   /**
   * Get password
   * @return password
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_PASSWORD)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getPassword() {
    return password;
  }


  @JsonProperty(JSON_PROPERTY_PASSWORD)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setPassword(String password) {
    this.password = password;
  }


  public RegisterRequest role(RoleEnum role) {
    
    this.role = role;
    return this;
  }

   /**
   * Get role
   * @return role
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_ROLE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public RoleEnum getRole() {
    return role;
  }


  @JsonProperty(JSON_PROPERTY_ROLE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setRole(RoleEnum role) {
    this.role = role;
  }


  public RegisterRequest kurum(KurumEnum kurum) {
    
    this.kurum = kurum;
    return this;
  }

   /**
   * Get kurum
   * @return kurum
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_KURUM)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public KurumEnum getKurum() {
    return kurum;
  }


  @JsonProperty(JSON_PROPERTY_KURUM)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setKurum(KurumEnum kurum) {
    this.kurum = kurum;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RegisterRequest registerRequest = (RegisterRequest) o;
    return Objects.equals(this.userName, registerRequest.userName) &&
        Objects.equals(this.password, registerRequest.password) &&
        Objects.equals(this.role, registerRequest.role) &&
        Objects.equals(this.kurum, registerRequest.kurum);
  }

  @Override
  public int hashCode() {
    return Objects.hash(userName, password, role, kurum);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RegisterRequest {\n");
    sb.append("    userName: ").append(toIndentedString(userName)).append("\n");
    sb.append("    password: ").append(toIndentedString(password)).append("\n");
    sb.append("    role: ").append(toIndentedString(role)).append("\n");
    sb.append("    kurum: ").append(toIndentedString(kurum)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

