/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.io.File;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 * MahkemeKoduGuncelleRequest
 */
@JsonPropertyOrder({
  MahkemeKararGuncelleRequest.JSON_PROPERTY_MAHKEME_KARAR_DOSYASI,
  MahkemeKararGuncelleRequest.JSON_PROPERTY_MAHKEME_KARAR_DETAY
})
@JsonTypeName("mahkemeKoduGuncelle_request")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class MahkemeKararGuncelleRequest {
  public static final String JSON_PROPERTY_MAHKEME_KARAR_DOSYASI = "mahkemeKararDosyasi";
  private File mahkemeKararDosyasi;

  public static final String JSON_PROPERTY_MAHKEME_KARAR_DETAY = "mahkemeKararDetay";
  private IDMahkemeKararGuncellemeRequest mahkemeKararDetay;

  public MahkemeKararGuncelleRequest() {
  }

  public MahkemeKararGuncelleRequest mahkemeKararDosyasi(File mahkemeKararDosyasi) {
    
    this.mahkemeKararDosyasi = mahkemeKararDosyasi;
    return this;
  }

   /**
   * Get mahkemeKararDosyasi
   * @return mahkemeKararDosyasi
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DOSYASI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public File getMahkemeKararDosyasi() {
    return mahkemeKararDosyasi;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DOSYASI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMahkemeKararDosyasi(File mahkemeKararDosyasi) {
    this.mahkemeKararDosyasi = mahkemeKararDosyasi;
  }


  public MahkemeKararGuncelleRequest mahkemeKararDetay(IDMahkemeKararGuncellemeRequest mahkemeKararDetay) {
    
    this.mahkemeKararDetay = mahkemeKararDetay;
    return this;
  }

   /**
   * Get mahkemeKararDetay
   * @return mahkemeKararDetay
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DETAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public IDMahkemeKararGuncellemeRequest getMahkemeKararDetay() {
    return mahkemeKararDetay;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DETAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMahkemeKararDetay(IDMahkemeKararGuncellemeRequest mahkemeKararDetay) {
    this.mahkemeKararDetay = mahkemeKararDetay;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MahkemeKararGuncelleRequest mahkemeKararGuncelleRequest = (MahkemeKararGuncelleRequest) o;
    return Objects.equals(this.mahkemeKararDosyasi, mahkemeKararGuncelleRequest.mahkemeKararDosyasi) &&
        Objects.equals(this.mahkemeKararDetay, mahkemeKararGuncelleRequest.mahkemeKararDetay);
  }

  @Override
  public int hashCode() {
    return Objects.hash(mahkemeKararDosyasi, mahkemeKararDetay);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MahkemeKoduGuncelleRequest {\n");
    sb.append("    mahkemeKararDosyasi: ").append(toIndentedString(mahkemeKararDosyasi)).append("\n");
    sb.append("    mahkemeKararDetay: ").append(toIndentedString(mahkemeKararDetay)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

