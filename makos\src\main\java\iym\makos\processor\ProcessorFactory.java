package iym.makos.processor;


import iym.makos.model.reqrep.MahkemeKararRequest;
import iym.makos.model.reqrep.MahkemeKararResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class ProcessorFactory {

    private final Map<Class<? extends MahkemeKararRequest>, Map<Class<? extends MahkemeKararResponse>, IMakosRequestProcessor<?, ?>>> processorsByRequest = new HashMap<>();

    @Autowired
    public ProcessorFactory(List<IMakosRequestProcessor<?, ?>> requestProcessors) {
        for (IMakosRequestProcessor<?, ?> processor : requestProcessors) {
            processorsByRequest.computeIfAbsent(processor.getRelatedRequestType(), map -> new HashMap<>())
                    .put(processor.getRelatedResponseType(), processor);
        }
    }

    @SuppressWarnings("unchecked")
    public <T extends MahkemeKararRequest, R extends MahkemeKararResponse> IMakosRequestProcessor<T, R> getProcessor(Class<T> requestType, Class<R> responseType) {
        Map<Class<? extends MahkemeKararResponse>, IMakosRequestProcessor<?, ?>> requestProcessorMap = processorsByRequest.get(requestType);
        if (requestProcessorMap != null)
            return (IMakosRequestProcessor<T, R>) requestProcessorMap.get(responseType);
        throw new RuntimeException("No processor found for <" + requestType.getSimpleName() + ", " + responseType.getSimpleName() + ">");
    }
}