package iym.backend.postgresql.kullanicigrup.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import iym.backend.postgresql.kullanicigrupyetki.entity.KullaniciGrupYetki;
import iym.backend.postgresql.shared.entity.BaseEntity;

import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "kullanici_gruplar")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KullaniciGrup extends BaseEntity {

    @Column(nullable = false)
    private String ad;

    @OneToMany(mappedBy = "kullaniciGrup", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<KullaniciGrupYetki> kullaniciGrupYetkiler = new ArrayList<>();
}
