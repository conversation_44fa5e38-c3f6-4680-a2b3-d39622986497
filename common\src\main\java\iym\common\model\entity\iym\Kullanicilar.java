package iym.common.model.entity.iym;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * Entity class for KULLANICILAR table
 * Exactly matches the DDL definition
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode

@Entity
@Table(name = "KULLANICILAR")
public class Kullanicilar implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "KULLANICILAR_SEQ")
    @SequenceGenerator(name = "KULLANICILAR_SEQ", sequenceName = "KULLANICILAR_SEQ", allocationSize = 1)
    @Column(name = "ID")
    private Long id;

    @Column(name = "ADI", nullable = false, length = 20)
    @NotNull
    @NotBlank
    @Size(max = 20)
    private String adi;

    @Column(name = "SOYADI", nullable = false, length = 40)
    @NotNull
    @NotBlank
    @Size(max = 40)
    private String soyadi;

    @Column(name = "KULLANICI_ADI", nullable = false, length = 20)
    @NotNull
    @NotBlank
    @Size(max = 20)
    private String kullaniciAdi;

    @Column(name = "SIFRE", nullable = false, length = 32, columnDefinition = "CHAR(32 BYTE)")
    @NotNull
    @NotBlank
    @Size(max = 32)
    private String sifre;

    @Column(name = "GOREVI", length = 6)
    private String gorevi;

    @Column(name = "YETKI")
    private Long yetki;

    @Column(name = "BIRIMI", length = 4)
    private String birimi;

    @Column(name = "GOREV_TANIMI", length = 300)
    private String gorevTanimi;

    @Column(name = "TEL", length = 16)
    private String tel;

    @Column(name = "POSTA", length = 55)
    private String posta;

    @Column(name = "FAX", length = 55)
    private String fax;

    @Column(name = "RESMI_KOD", length = 12)
    private String resmiKod;

    @Column(name = "KIM_KUL_ID")
    private Long kimKulId;

    @Column(name = "DURUMU", length = 20)
    private String durumu;

    @Column(name = "IMZA_YETKISI", length = 1, columnDefinition = "CHAR(1)")
    private String imzaYetkisi;

    @Column(name = "IMZA_DOSYASI", length = 50)
    private String imzaDosyasi;

    @Column(name = "EKSTRAGUVENLIK", length = 1)
    private String ekstraGuvenlik;

    @Column(name = "TEMSIL_EDILEN_KURUM", length = 2)
    private String temsilEdilenKurum;

    @Column(name = "CID", length = 32)
    private String cid;

    @Column(name = "TCK", length = 11)
    private String tck;

    @Column(name = "PAROLA_DEGISIM_TARIHI")
    @Temporal(TemporalType.DATE)
    private Date parolaDegisimTarihi;

    @Column(name = "AKADEMIK_UNVAN", length = 10)
    private String akademikUnvan;

    @Column(name = "GRUP_KODU", length = 10)
    private String grupKodu;
}
