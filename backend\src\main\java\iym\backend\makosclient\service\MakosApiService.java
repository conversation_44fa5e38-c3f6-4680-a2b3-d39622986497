package iym.backend.makosclient.service;

import iym.makos.api.client.gen.api.HealthCheckControllerApi;
import iym.makos.api.client.gen.api.MahkemeKararControllerApi;
import iym.makos.api.client.gen.model.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

import java.io.File;

/**
 * MAKOS API Service
 * Generated API client'ı kullanarak MAKOS işlemlerini gerçekleştirir
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MakosApiService {

    private final MahkemeKararControllerApi mahkemeKararControllerApi;
    private final HealthCheckControllerApi healthCheckControllerApi;

    /**
     * Health check işlemi
     *
     * @return String response
     */
    public HealthCheckResponse healthCheck() {
        try {
            log.info("MAKOS health check başlatılıyor...");
            HealthCheckResponse response = healthCheckControllerApi.healthCheck();
            log.info("MAKOS health check başarılı: {}", response);
            return response;
        } catch (RestClientException e) {
            log.error("MAKOS health check başarısız", e);
            throw new RuntimeException("MAKOS health check başarısız", e);
        }
    }

    // Diğer MAKOS API metodları buraya taşınacak...
    // TODO: Mevcut MakosApiService'den tüm metodları buraya taşı
}
