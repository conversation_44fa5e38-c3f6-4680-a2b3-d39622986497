package iym.db.jpa.dao;

import iym.common.model.entity.iym.MahkemeKararBilgiGuncellemeTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Repository interface for MahkemeKoduDetayTalepRepo entity
 */
@Repository
public interface MahkemeKararBilgiGuncelleTalepRepo extends JpaRepository<MahkemeKararBilgiGuncellemeTalep, Long> {

    Optional<MahkemeKararBilgiGuncellemeTalep> findByMahkemeKararDetayId(Long mahkemeKararTalepId);

}
