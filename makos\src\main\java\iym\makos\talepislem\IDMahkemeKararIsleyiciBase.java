package iym.makos.talepislem;

import iym.common.model.entity.iym.EvrakKayit;
import iym.common.model.entity.iym.MahkemeKararTalep;
import iym.db.jpa.dao.EvrakKayitRepo;
import iym.db.jpa.dao.MahkemeKararTalepRepo;
import iym.makos.dto.talepupdate.MahkemeKararTalepUpdateRequest;
import iym.makos.dto.talepupdate.MahkemeKararTalepUpdateResponse;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Slf4j
public abstract class IDMahkemeKararIsleyiciBase implements MahkemeKararTalepIsleyici {

    protected EvrakKayitRepo evrakKayitRepo;
    protected MahkemeKararTalepRepo mahkemeKararTalepRepo;

    @Autowired
    public final void setEvrakKayitRepo(EvrakKayitRepo evrakKayitRepo) {
        this.evrakKayitRepo = evrakKayitRepo;
    }

    @Autowired
    public final void setMahkemeKararTalepRepo(MahkemeKararTalepRepo mahkemeKararTalepRepo) {
        this.mahkemeKararTalepRepo = mahkemeKararTalepRepo;
    }

    @Override
    @Transactional
    public MahkemeKararTalepUpdateResponse process(MahkemeKararTalepUpdateRequest request, Long kullaniciId) {
        MahkemeKararTalepUpdateResponse processResponse = process(request);
        if (!processResponse.isSuccess()) {
            return processResponse;
        }

        return updateRelatedTables(request);
    }

    protected abstract MahkemeKararTalepUpdateResponse updateRelatedTables(MahkemeKararTalepUpdateRequest request);

    private MahkemeKararTalepUpdateResponse process(MahkemeKararTalepUpdateRequest request) {

        try{

            String durum = MahkemeKararTalepIsleyici.toDurum(request.getTalepGuncellemeTuru());
            Optional<MahkemeKararTalep> mahkemeKararTalepOpt = mahkemeKararTalepRepo.findById(request.getMahkemeKararTalepId());
            if (mahkemeKararTalepOpt.isEmpty()) {
                throw new Exception("talep bulunamadı");
            }

            MahkemeKararTalep mahkemeKararTalep = mahkemeKararTalepOpt.get();

            //durum degisikligini kaydet
            mahkemeKararTalep.setDurum(durum);
            mahkemeKararTalepRepo.save(mahkemeKararTalep);

            Long evrakId = mahkemeKararTalep.getEvrakId();
            Optional<EvrakKayit> evrakKayitOpt = evrakKayitRepo.findById(evrakId);
            if (evrakKayitOpt.isEmpty()) {
                throw new Exception("Evrak kayıt bulunamadı");
            }
            EvrakKayit evrakKayit = evrakKayitOpt.get();

            //durum degisikligini kaydet
            evrakKayit.setDurumu(durum);
            evrakKayitRepo.save(evrakKayit);

            return MahkemeKararTalepUpdateResponse.builder()
                    .requestId(request.getId())
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .build())
                    .build();
        }catch (Exception ex){
            log.error("MahkemeKararTalepUpdateRequest process failed, requestId:{}, mahkemeKararTalepId:{}", request.getId(), request.getMahkemeKararTalepId(), ex);
            return MahkemeKararTalepUpdateResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Mahkeme karar talep veya evrak guncelleme hatası")
                            .build())
                    .build();
        }
    }

}

