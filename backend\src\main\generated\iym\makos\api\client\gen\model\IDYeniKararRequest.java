/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import iym.makos.api.client.gen.model.EvrakDetay;
import iym.makos.api.client.gen.model.HedefDetayID;
import iym.makos.api.client.gen.model.MahkemeKararBilgisi;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * ID Mahkeme Karar Detaylari
 */
@JsonPropertyOrder({
  IDYeniKararRequest.JSON_PROPERTY_ID,
  IDYeniKararRequest.JSON_PROPERTY_KARAR_TURU,
  IDYeniKararRequest.JSON_PROPERTY_EVRAK_DETAY,
  IDYeniKararRequest.JSON_PROPERTY_MAHKEME_KARAR_BILGISI,
  IDYeniKararRequest.JSON_PROPERTY_HEDEF_DETAY_LISTESI,
  IDYeniKararRequest.JSON_PROPERTY_MAHKEME_AIDIYAT_KODLARI,
  IDYeniKararRequest.JSON_PROPERTY_MAHKEME_SUC_TIPI_KODLARI
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class IDYeniKararRequest {
  public static final String JSON_PROPERTY_ID = "id";
  private UUID id;

  /**
   * Gets or Sets kararTuru
   */
  public enum KararTuruEnum {
    _0("0"),
    
    _1("1"),
    
    _2("2"),
    
    _3("3"),
    
    _4("4"),
    
    _5("5"),
    
    _6("6"),
    
    _7("7"),
    
    _8("8"),
    
    _9("9");

    private String value;

    KararTuruEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static KararTuruEnum fromValue(String value) {
      for (KararTuruEnum b : KararTuruEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_KARAR_TURU = "kararTuru";
  private KararTuruEnum kararTuru;

  public static final String JSON_PROPERTY_EVRAK_DETAY = "evrakDetay";
  private EvrakDetay evrakDetay;

  public static final String JSON_PROPERTY_MAHKEME_KARAR_BILGISI = "mahkemeKararBilgisi";
  private MahkemeKararBilgisi mahkemeKararBilgisi;

  public static final String JSON_PROPERTY_HEDEF_DETAY_LISTESI = "hedefDetayListesi";
  private List<HedefDetayID> hedefDetayListesi = new ArrayList<>();

  public static final String JSON_PROPERTY_MAHKEME_AIDIYAT_KODLARI = "mahkemeAidiyatKodlari";
  private List<String> mahkemeAidiyatKodlari;

  public static final String JSON_PROPERTY_MAHKEME_SUC_TIPI_KODLARI = "mahkemeSucTipiKodlari";
  private List<String> mahkemeSucTipiKodlari;

  public IDYeniKararRequest() {
  }

  public IDYeniKararRequest id(UUID id) {
    
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public UUID getId() {
    return id;
  }


  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setId(UUID id) {
    this.id = id;
  }


  public IDYeniKararRequest kararTuru(KararTuruEnum kararTuru) {
    
    this.kararTuru = kararTuru;
    return this;
  }

   /**
   * Get kararTuru
   * @return kararTuru
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_KARAR_TURU)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public KararTuruEnum getKararTuru() {
    return kararTuru;
  }


  @JsonProperty(JSON_PROPERTY_KARAR_TURU)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setKararTuru(KararTuruEnum kararTuru) {
    this.kararTuru = kararTuru;
  }


  public IDYeniKararRequest evrakDetay(EvrakDetay evrakDetay) {
    
    this.evrakDetay = evrakDetay;
    return this;
  }

   /**
   * Get evrakDetay
   * @return evrakDetay
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_EVRAK_DETAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public EvrakDetay getEvrakDetay() {
    return evrakDetay;
  }


  @JsonProperty(JSON_PROPERTY_EVRAK_DETAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setEvrakDetay(EvrakDetay evrakDetay) {
    this.evrakDetay = evrakDetay;
  }


  public IDYeniKararRequest mahkemeKararBilgisi(MahkemeKararBilgisi mahkemeKararBilgisi) {
    
    this.mahkemeKararBilgisi = mahkemeKararBilgisi;
    return this;
  }

   /**
   * Get mahkemeKararBilgisi
   * @return mahkemeKararBilgisi
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_BILGISI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public MahkemeKararBilgisi getMahkemeKararBilgisi() {
    return mahkemeKararBilgisi;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_BILGISI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMahkemeKararBilgisi(MahkemeKararBilgisi mahkemeKararBilgisi) {
    this.mahkemeKararBilgisi = mahkemeKararBilgisi;
  }


  public IDYeniKararRequest hedefDetayListesi(List<HedefDetayID> hedefDetayListesi) {
    
    this.hedefDetayListesi = hedefDetayListesi;
    return this;
  }

  public IDYeniKararRequest addHedefDetayListesiItem(HedefDetayID hedefDetayListesiItem) {
    if (this.hedefDetayListesi == null) {
      this.hedefDetayListesi = new ArrayList<>();
    }
    this.hedefDetayListesi.add(hedefDetayListesiItem);
    return this;
  }

   /**
   * Get hedefDetayListesi
   * @return hedefDetayListesi
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_HEDEF_DETAY_LISTESI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public List<HedefDetayID> getHedefDetayListesi() {
    return hedefDetayListesi;
  }


  @JsonProperty(JSON_PROPERTY_HEDEF_DETAY_LISTESI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setHedefDetayListesi(List<HedefDetayID> hedefDetayListesi) {
    this.hedefDetayListesi = hedefDetayListesi;
  }


  public IDYeniKararRequest mahkemeAidiyatKodlari(List<String> mahkemeAidiyatKodlari) {
    
    this.mahkemeAidiyatKodlari = mahkemeAidiyatKodlari;
    return this;
  }

  public IDYeniKararRequest addMahkemeAidiyatKodlariItem(String mahkemeAidiyatKodlariItem) {
    if (this.mahkemeAidiyatKodlari == null) {
      this.mahkemeAidiyatKodlari = new ArrayList<>();
    }
    this.mahkemeAidiyatKodlari.add(mahkemeAidiyatKodlariItem);
    return this;
  }

   /**
   * Get mahkemeAidiyatKodlari
   * @return mahkemeAidiyatKodlari
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MAHKEME_AIDIYAT_KODLARI)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<String> getMahkemeAidiyatKodlari() {
    return mahkemeAidiyatKodlari;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_AIDIYAT_KODLARI)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMahkemeAidiyatKodlari(List<String> mahkemeAidiyatKodlari) {
    this.mahkemeAidiyatKodlari = mahkemeAidiyatKodlari;
  }


  public IDYeniKararRequest mahkemeSucTipiKodlari(List<String> mahkemeSucTipiKodlari) {
    
    this.mahkemeSucTipiKodlari = mahkemeSucTipiKodlari;
    return this;
  }

  public IDYeniKararRequest addMahkemeSucTipiKodlariItem(String mahkemeSucTipiKodlariItem) {
    if (this.mahkemeSucTipiKodlari == null) {
      this.mahkemeSucTipiKodlari = new ArrayList<>();
    }
    this.mahkemeSucTipiKodlari.add(mahkemeSucTipiKodlariItem);
    return this;
  }

   /**
   * Get mahkemeSucTipiKodlari
   * @return mahkemeSucTipiKodlari
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MAHKEME_SUC_TIPI_KODLARI)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<String> getMahkemeSucTipiKodlari() {
    return mahkemeSucTipiKodlari;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_SUC_TIPI_KODLARI)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMahkemeSucTipiKodlari(List<String> mahkemeSucTipiKodlari) {
    this.mahkemeSucTipiKodlari = mahkemeSucTipiKodlari;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    IDYeniKararRequest idYeniKararRequest = (IDYeniKararRequest) o;
    return Objects.equals(this.id, idYeniKararRequest.id) &&
        Objects.equals(this.kararTuru, idYeniKararRequest.kararTuru) &&
        Objects.equals(this.evrakDetay, idYeniKararRequest.evrakDetay) &&
        Objects.equals(this.mahkemeKararBilgisi, idYeniKararRequest.mahkemeKararBilgisi) &&
        Objects.equals(this.hedefDetayListesi, idYeniKararRequest.hedefDetayListesi) &&
        Objects.equals(this.mahkemeAidiyatKodlari, idYeniKararRequest.mahkemeAidiyatKodlari) &&
        Objects.equals(this.mahkemeSucTipiKodlari, idYeniKararRequest.mahkemeSucTipiKodlari);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, kararTuru, evrakDetay, mahkemeKararBilgisi, hedefDetayListesi, mahkemeAidiyatKodlari, mahkemeSucTipiKodlari);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class IDYeniKararRequest {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    kararTuru: ").append(toIndentedString(kararTuru)).append("\n");
    sb.append("    evrakDetay: ").append(toIndentedString(evrakDetay)).append("\n");
    sb.append("    mahkemeKararBilgisi: ").append(toIndentedString(mahkemeKararBilgisi)).append("\n");
    sb.append("    hedefDetayListesi: ").append(toIndentedString(hedefDetayListesi)).append("\n");
    sb.append("    mahkemeAidiyatKodlari: ").append(toIndentedString(mahkemeAidiyatKodlari)).append("\n");
    sb.append("    mahkemeSucTipiKodlari: ").append(toIndentedString(mahkemeSucTipiKodlari)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

