/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * ChangePasswordRequest
 */
@JsonPropertyOrder({
  ChangePasswordRequest.JSON_PROPERTY_CURRENT_PASSWORD,
  ChangePasswordRequest.JSON_PROPERTY_NEW_PASSWORD,
  ChangePasswordRequest.JSON_PROPERTY_CONFIRM_PASSWORD
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class ChangePasswordRequest {
  public static final String JSON_PROPERTY_CURRENT_PASSWORD = "currentPassword";
  private String currentPassword;

  public static final String JSON_PROPERTY_NEW_PASSWORD = "newPassword";
  private String newPassword;

  public static final String JSON_PROPERTY_CONFIRM_PASSWORD = "confirmPassword";
  private String confirmPassword;

  public ChangePasswordRequest() {
  }

  public ChangePasswordRequest currentPassword(String currentPassword) {
    
    this.currentPassword = currentPassword;
    return this;
  }

   /**
   * Get currentPassword
   * @return currentPassword
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_CURRENT_PASSWORD)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getCurrentPassword() {
    return currentPassword;
  }


  @JsonProperty(JSON_PROPERTY_CURRENT_PASSWORD)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setCurrentPassword(String currentPassword) {
    this.currentPassword = currentPassword;
  }


  public ChangePasswordRequest newPassword(String newPassword) {
    
    this.newPassword = newPassword;
    return this;
  }

   /**
   * Get newPassword
   * @return newPassword
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_NEW_PASSWORD)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getNewPassword() {
    return newPassword;
  }


  @JsonProperty(JSON_PROPERTY_NEW_PASSWORD)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setNewPassword(String newPassword) {
    this.newPassword = newPassword;
  }


  public ChangePasswordRequest confirmPassword(String confirmPassword) {
    
    this.confirmPassword = confirmPassword;
    return this;
  }

   /**
   * Get confirmPassword
   * @return confirmPassword
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_CONFIRM_PASSWORD)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getConfirmPassword() {
    return confirmPassword;
  }


  @JsonProperty(JSON_PROPERTY_CONFIRM_PASSWORD)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setConfirmPassword(String confirmPassword) {
    this.confirmPassword = confirmPassword;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ChangePasswordRequest changePasswordRequest = (ChangePasswordRequest) o;
    return Objects.equals(this.currentPassword, changePasswordRequest.currentPassword) &&
        Objects.equals(this.newPassword, changePasswordRequest.newPassword) &&
        Objects.equals(this.confirmPassword, changePasswordRequest.confirmPassword);
  }

  @Override
  public int hashCode() {
    return Objects.hash(currentPassword, newPassword, confirmPassword);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ChangePasswordRequest {\n");
    sb.append("    currentPassword: ").append(toIndentedString(currentPassword)).append("\n");
    sb.append("    newPassword: ").append(toIndentedString(newPassword)).append("\n");
    sb.append("    confirmPassword: ").append(toIndentedString(confirmPassword)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

