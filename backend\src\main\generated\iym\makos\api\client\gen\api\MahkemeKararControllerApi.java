package iym.makos.api.client.gen.api;

import iym.makos.api.client.gen.handler.ApiClient;

import java.io.File;
import iym.makos.api.client.gen.model.IDAidiyatBilgisiGuncellemeRequest;
import iym.makos.api.client.gen.model.IDAidiyatBilgisiGuncellemeResponse;
import iym.makos.api.client.gen.model.IDHedefGuncellemeRequest;
import iym.makos.api.client.gen.model.IDHedefGuncellemeResponse;
import iym.makos.api.client.gen.model.IDMahkemeKararGuncellemeRequest;
import iym.makos.api.client.gen.model.IDMahkemeKararGuncellemeResponse;
import iym.makos.api.client.gen.model.IDSonlandirmaKarariRequest;
import iym.makos.api.client.gen.model.IDSonlandirmaKarariResponse;
import iym.makos.api.client.gen.model.IDSucTipiGuncellemeRequest;
import iym.makos.api.client.gen.model.IDSucTipiGuncellemeResponse;
import iym.makos.api.client.gen.model.IDUzatmaKarariRequest;
import iym.makos.api.client.gen.model.IDUzatmaKarariResponse;
import iym.makos.api.client.gen.model.IDYeniKararRequest;
import iym.makos.api.client.gen.model.IDYeniKararResponse;
import iym.makos.api.client.gen.model.ITKararRequest;
import iym.makos.api.client.gen.model.ITKararResponse;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class MahkemeKararControllerApi {
    private ApiClient apiClient;

    public MahkemeKararControllerApi() {
        this(new ApiClient());
    }

    public MahkemeKararControllerApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param mahkemeKararDosyasi  (required)
     * @param mahkemeKararDetay  (required)
     * @return IDAidiyatBilgisiGuncellemeResponse
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public IDAidiyatBilgisiGuncellemeResponse aidiyatBilgisiGuncelle(File mahkemeKararDosyasi, IDAidiyatBilgisiGuncellemeRequest mahkemeKararDetay) throws RestClientException {
        return aidiyatBilgisiGuncelleWithHttpInfo(mahkemeKararDosyasi, mahkemeKararDetay).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param mahkemeKararDosyasi  (required)
     * @param mahkemeKararDetay  (required)
     * @return ResponseEntity&lt;IDAidiyatBilgisiGuncellemeResponse&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<IDAidiyatBilgisiGuncellemeResponse> aidiyatBilgisiGuncelleWithHttpInfo(File mahkemeKararDosyasi, IDAidiyatBilgisiGuncellemeRequest mahkemeKararDetay) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'mahkemeKararDosyasi' is set
        if (mahkemeKararDosyasi == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'mahkemeKararDosyasi' when calling aidiyatBilgisiGuncelle");
        }
        
        // verify the required parameter 'mahkemeKararDetay' is set
        if (mahkemeKararDetay == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'mahkemeKararDetay' when calling aidiyatBilgisiGuncelle");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        if (mahkemeKararDosyasi != null)
            localVarFormParams.add("mahkemeKararDosyasi", new FileSystemResource(mahkemeKararDosyasi));
        if (mahkemeKararDetay != null)
            localVarFormParams.add("mahkemeKararDetay", mahkemeKararDetay);

        final String[] localVarAccepts = { 
            "*/*"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "multipart/form-data"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "BasicAuth" };

        ParameterizedTypeReference<IDAidiyatBilgisiGuncellemeResponse> localReturnType = new ParameterizedTypeReference<IDAidiyatBilgisiGuncellemeResponse>() {};
        return apiClient.invokeAPI("/mahkemeKarar/aidiyatBilgisiGuncelle", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param mahkemeKararDosyasi  (required)
     * @param mahkemeKararDetay  (required)
     * @return IDHedefGuncellemeResponse
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public IDHedefGuncellemeResponse hedefBilgiGuncelle(File mahkemeKararDosyasi, IDHedefGuncellemeRequest mahkemeKararDetay) throws RestClientException {
        return hedefBilgiGuncelleWithHttpInfo(mahkemeKararDosyasi, mahkemeKararDetay).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param mahkemeKararDosyasi  (required)
     * @param mahkemeKararDetay  (required)
     * @return ResponseEntity&lt;IDHedefGuncellemeResponse&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<IDHedefGuncellemeResponse> hedefBilgiGuncelleWithHttpInfo(File mahkemeKararDosyasi, IDHedefGuncellemeRequest mahkemeKararDetay) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'mahkemeKararDosyasi' is set
        if (mahkemeKararDosyasi == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'mahkemeKararDosyasi' when calling hedefBilgiGuncelle");
        }
        
        // verify the required parameter 'mahkemeKararDetay' is set
        if (mahkemeKararDetay == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'mahkemeKararDetay' when calling hedefBilgiGuncelle");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        if (mahkemeKararDosyasi != null)
            localVarFormParams.add("mahkemeKararDosyasi", new FileSystemResource(mahkemeKararDosyasi));
        if (mahkemeKararDetay != null)
            localVarFormParams.add("mahkemeKararDetay", mahkemeKararDetay);

        final String[] localVarAccepts = { 
            "*/*"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "multipart/form-data"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "BasicAuth" };

        ParameterizedTypeReference<IDHedefGuncellemeResponse> localReturnType = new ParameterizedTypeReference<IDHedefGuncellemeResponse>() {};
        return apiClient.invokeAPI("/mahkemeKarar/hedefBilgiGuncelle", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param mahkemeKararDosyasiIT  (required)
     * @param mahkemeKararDetayIT  (required)
     * @return ITKararResponse
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ITKararResponse kararGonderIT(File mahkemeKararDosyasiIT, ITKararRequest mahkemeKararDetayIT) throws RestClientException {
        return kararGonderITWithHttpInfo(mahkemeKararDosyasiIT, mahkemeKararDetayIT).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param mahkemeKararDosyasiIT  (required)
     * @param mahkemeKararDetayIT  (required)
     * @return ResponseEntity&lt;ITKararResponse&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ITKararResponse> kararGonderITWithHttpInfo(File mahkemeKararDosyasiIT, ITKararRequest mahkemeKararDetayIT) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'mahkemeKararDosyasiIT' is set
        if (mahkemeKararDosyasiIT == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'mahkemeKararDosyasiIT' when calling kararGonderIT");
        }
        
        // verify the required parameter 'mahkemeKararDetayIT' is set
        if (mahkemeKararDetayIT == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'mahkemeKararDetayIT' when calling kararGonderIT");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        if (mahkemeKararDosyasiIT != null)
            localVarFormParams.add("mahkemeKararDosyasiIT", new FileSystemResource(mahkemeKararDosyasiIT));
        if (mahkemeKararDetayIT != null)
            localVarFormParams.add("mahkemeKararDetayIT", mahkemeKararDetayIT);

        final String[] localVarAccepts = { 
            "*/*"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "multipart/form-data"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "BasicAuth" };

        ParameterizedTypeReference<ITKararResponse> localReturnType = new ParameterizedTypeReference<ITKararResponse>() {};
        return apiClient.invokeAPI("/mahkemeKarar/kararGonderIT", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param mahkemeKararDosyasi  (required)
     * @param mahkemeKararDetay  (required)
     * @return IDMahkemeKararGuncellemeResponse
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public IDMahkemeKararGuncellemeResponse mahkemeBilgiGuncelle(File mahkemeKararDosyasi, IDMahkemeKararGuncellemeRequest mahkemeKararDetay) throws RestClientException {
        return mahkemeBilgiGuncelleWithHttpInfo(mahkemeKararDosyasi, mahkemeKararDetay).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param mahkemeKararDosyasi  (required)
     * @param mahkemeKararDetay  (required)
     * @return ResponseEntity&lt;IDMahkemeKararGuncellemeResponse&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<IDMahkemeKararGuncellemeResponse> mahkemeBilgiGuncelleWithHttpInfo(File mahkemeKararDosyasi, IDMahkemeKararGuncellemeRequest mahkemeKararDetay) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'mahkemeKararDosyasi' is set
        if (mahkemeKararDosyasi == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'mahkemeKararDosyasi' when calling mahkemeBilgiGuncelle");
        }
        
        // verify the required parameter 'mahkemeKararDetay' is set
        if (mahkemeKararDetay == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'mahkemeKararDetay' when calling mahkemeBilgiGuncelle");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        if (mahkemeKararDosyasi != null)
            localVarFormParams.add("mahkemeKararDosyasi", new FileSystemResource(mahkemeKararDosyasi));
        if (mahkemeKararDetay != null)
            localVarFormParams.add("mahkemeKararDetay", mahkemeKararDetay);

        final String[] localVarAccepts = { 
            "*/*"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "multipart/form-data"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "BasicAuth" };

        ParameterizedTypeReference<IDMahkemeKararGuncellemeResponse> localReturnType = new ParameterizedTypeReference<IDMahkemeKararGuncellemeResponse>() {};
        return apiClient.invokeAPI("/mahkemeKarar/mahkemeBilgiGuncelle", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param mahkemeKararDosyasiID  (required)
     * @param mahkemeKararDetayID  (required)
     * @return IDSonlandirmaKarariResponse
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public IDSonlandirmaKarariResponse sonlandirmaKarariID(File mahkemeKararDosyasiID, IDSonlandirmaKarariRequest mahkemeKararDetayID) throws RestClientException {
        return sonlandirmaKarariIDWithHttpInfo(mahkemeKararDosyasiID, mahkemeKararDetayID).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param mahkemeKararDosyasiID  (required)
     * @param mahkemeKararDetayID  (required)
     * @return ResponseEntity&lt;IDSonlandirmaKarariResponse&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<IDSonlandirmaKarariResponse> sonlandirmaKarariIDWithHttpInfo(File mahkemeKararDosyasiID, IDSonlandirmaKarariRequest mahkemeKararDetayID) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'mahkemeKararDosyasiID' is set
        if (mahkemeKararDosyasiID == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'mahkemeKararDosyasiID' when calling sonlandirmaKarariID");
        }
        
        // verify the required parameter 'mahkemeKararDetayID' is set
        if (mahkemeKararDetayID == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'mahkemeKararDetayID' when calling sonlandirmaKarariID");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        if (mahkemeKararDosyasiID != null)
            localVarFormParams.add("mahkemeKararDosyasiID", new FileSystemResource(mahkemeKararDosyasiID));
        if (mahkemeKararDetayID != null)
            localVarFormParams.add("mahkemeKararDetayID", mahkemeKararDetayID);

        final String[] localVarAccepts = { 
            "*/*"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "multipart/form-data"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "BasicAuth" };

        ParameterizedTypeReference<IDSonlandirmaKarariResponse> localReturnType = new ParameterizedTypeReference<IDSonlandirmaKarariResponse>() {};
        return apiClient.invokeAPI("/mahkemeKarar/sonlandirmaKarariID", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param mahkemeKararDosyasi  (required)
     * @param mahkemeKararDetay  (required)
     * @return IDSucTipiGuncellemeResponse
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public IDSucTipiGuncellemeResponse sucTipiGuncelle(File mahkemeKararDosyasi, IDSucTipiGuncellemeRequest mahkemeKararDetay) throws RestClientException {
        return sucTipiGuncelleWithHttpInfo(mahkemeKararDosyasi, mahkemeKararDetay).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param mahkemeKararDosyasi  (required)
     * @param mahkemeKararDetay  (required)
     * @return ResponseEntity&lt;IDSucTipiGuncellemeResponse&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<IDSucTipiGuncellemeResponse> sucTipiGuncelleWithHttpInfo(File mahkemeKararDosyasi, IDSucTipiGuncellemeRequest mahkemeKararDetay) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'mahkemeKararDosyasi' is set
        if (mahkemeKararDosyasi == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'mahkemeKararDosyasi' when calling sucTipiGuncelle");
        }
        
        // verify the required parameter 'mahkemeKararDetay' is set
        if (mahkemeKararDetay == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'mahkemeKararDetay' when calling sucTipiGuncelle");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        if (mahkemeKararDosyasi != null)
            localVarFormParams.add("mahkemeKararDosyasi", new FileSystemResource(mahkemeKararDosyasi));
        if (mahkemeKararDetay != null)
            localVarFormParams.add("mahkemeKararDetay", mahkemeKararDetay);

        final String[] localVarAccepts = { 
            "*/*"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "multipart/form-data"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "BasicAuth" };

        ParameterizedTypeReference<IDSucTipiGuncellemeResponse> localReturnType = new ParameterizedTypeReference<IDSucTipiGuncellemeResponse>() {};
        return apiClient.invokeAPI("/mahkemeKarar/sucTipiGuncelle", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param mahkemeKararDosyasiID  (required)
     * @param mahkemeKararDetayID  (required)
     * @return IDUzatmaKarariResponse
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public IDUzatmaKarariResponse uzatmaKarariID(File mahkemeKararDosyasiID, IDUzatmaKarariRequest mahkemeKararDetayID) throws RestClientException {
        return uzatmaKarariIDWithHttpInfo(mahkemeKararDosyasiID, mahkemeKararDetayID).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param mahkemeKararDosyasiID  (required)
     * @param mahkemeKararDetayID  (required)
     * @return ResponseEntity&lt;IDUzatmaKarariResponse&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<IDUzatmaKarariResponse> uzatmaKarariIDWithHttpInfo(File mahkemeKararDosyasiID, IDUzatmaKarariRequest mahkemeKararDetayID) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'mahkemeKararDosyasiID' is set
        if (mahkemeKararDosyasiID == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'mahkemeKararDosyasiID' when calling uzatmaKarariID");
        }
        
        // verify the required parameter 'mahkemeKararDetayID' is set
        if (mahkemeKararDetayID == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'mahkemeKararDetayID' when calling uzatmaKarariID");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        if (mahkemeKararDosyasiID != null)
            localVarFormParams.add("mahkemeKararDosyasiID", new FileSystemResource(mahkemeKararDosyasiID));
        if (mahkemeKararDetayID != null)
            localVarFormParams.add("mahkemeKararDetayID", mahkemeKararDetayID);

        final String[] localVarAccepts = { 
            "*/*"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "multipart/form-data"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "BasicAuth" };

        ParameterizedTypeReference<IDUzatmaKarariResponse> localReturnType = new ParameterizedTypeReference<IDUzatmaKarariResponse>() {};
        return apiClient.invokeAPI("/mahkemeKarar/uzatmaKarariID", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param mahkemeKararDosyasiID  (required)
     * @param jsonData  (required)
     * @return IDYeniKararResponse
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public IDYeniKararResponse yeniKararID(File mahkemeKararDosyasiID, IDYeniKararRequest jsonData) throws RestClientException {
        return yeniKararIDWithHttpInfo(mahkemeKararDosyasiID, jsonData).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param mahkemeKararDosyasiID  (required)
     * @param jsonData  (required)
     * @return ResponseEntity&lt;IDYeniKararResponse&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<IDYeniKararResponse> yeniKararIDWithHttpInfo(File mahkemeKararDosyasiID, IDYeniKararRequest jsonData) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'mahkemeKararDosyasiID' is set
        if (mahkemeKararDosyasiID == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'mahkemeKararDosyasiID' when calling yeniKararID");
        }
        
        // verify the required parameter 'jsonData' is set
        if (jsonData == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'jsonData' when calling yeniKararID");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        if (mahkemeKararDosyasiID != null)
            localVarFormParams.add("mahkemeKararDosyasiID", new FileSystemResource(mahkemeKararDosyasiID));
        if (jsonData != null)
            localVarFormParams.add("jsonData", jsonData);

        final String[] localVarAccepts = { 
            "*/*"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "multipart/form-data"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "BasicAuth" };

        ParameterizedTypeReference<IDYeniKararResponse> localReturnType = new ParameterizedTypeReference<IDYeniKararResponse>() {};
        return apiClient.invokeAPI("/mahkemeKarar/yeniKararID", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
}
