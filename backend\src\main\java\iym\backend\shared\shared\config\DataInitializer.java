package iym.backend.shared.config;

import org.springframework.transaction.annotation.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;
import iym.backend.kullanici.entity.Kullanici;
import iym.backend.kullanici.enums.enumKullaniciStatus;
import iym.backend.kullanici.repository.KullaniciRepository;
import iym.backend.kullanicigrup.entity.KullaniciGrup;
import iym.backend.kullanicigrup.repository.KullaniciGrupRepository;
import iym.backend.kullanicigrupyetki.entity.KullaniciGrupYetki;
import iym.backend.kullanicikullanicigrup.entity.KullaniciKullaniciGrup;
import iym.backend.menuitem.entity.MenuItem;
import iym.backend.menuitem.repository.MenuItemRepository;
import iym.backend.menuitemyetki.entity.MenuItemYetki;
import iym.backend.ulke.entity.Ulke;
import iym.backend.ulke.repository.UlkeRepository;
import iym.backend.yetki.entity.Yetki;
import iym.backend.yetki.repository.YetkiRepository;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Component
@ConditionalOnProperty(name = "app.seed-data", havingValue = "true", matchIfMissing = false)
@RequiredArgsConstructor
@Slf4j
public class DataInitializer implements CommandLineRunner {

    private final KullaniciRepository kullaniciRepository;
    private final KullaniciGrupRepository kullaniciGrupRepository;
    private final YetkiRepository yetkiRepository;
    private final MenuItemRepository menuItemRepository;
    private final PasswordEncoder passwordEncoder;
    private final UlkeRepository ulkeRepository;


    @Override
    @Transactional("postgresqlTransactionManager")
    public void run(String... args) throws Exception {
        log.info("=== Database Seed Data Initialization Starting ===");
        log.info("Loading initial data into PostgreSQL database...");
        log.info("YetkiRepository class: {}", yetkiRepository.getClass().getName());

        // 1. Yetkiler
        log.info("Checking yetkiler count...");
        if (yetkiRepository.count() == 0) {
            List<Yetki> yetkiler = List.of(
                    new Yetki("YETKI_VIEW", "Yetki"),
                    new Yetki("YETKI_MANAGE", "Yetki"),
                    new Yetki("KULLANICI_VIEW", "Kullanıcı"),
                    new Yetki("KULLANICI_MANAGE", "Kullanıcı"),
                    new Yetki("MENU_VIEW", "Menu"),
                    new Yetki("MENU_MANAGE", "Menu"),
                    new Yetki("ULKE_VIEW", "Ulke"),
                    new Yetki("ULKE_MANAGE", "Ulke")
            );
            yetkiRepository.saveAll(yetkiler);
        }

        // 2. Kullanıcı Grubu
        KullaniciGrup adminGrup = kullaniciGrupRepository.findByAd("Yönetici Grubu")
                .orElseGet(() -> {
                    KullaniciGrup grup = new KullaniciGrup();
                    grup.setAd("Yönetici Grubu");

                    List<Yetki> yetkiler = yetkiRepository.findAll();
                    for (Yetki yetki : yetkiler) {
                        KullaniciGrupYetki kgy = new KullaniciGrupYetki();
                        kgy.setKullaniciGrup(grup);
                        kgy.setYetki(yetki);
                        grup.getKullaniciGrupYetkiler().add(kgy);
                    }

                    return kullaniciGrupRepository.save(grup);
                });

        // 3. Kullanıcı
        if (kullaniciRepository.findByKullaniciAdi("admin").isEmpty()) {
            Kullanici kullanici = new Kullanici();
            kullanici.setKullaniciAdi("admin");
            kullanici.setAd("Sistem");
            kullanici.setSoyad("Yöneticisi");
            kullanici.setEmail("<EMAIL>");
            kullanici.setParola(passwordEncoder.encode("1"));
            kullanici.setStatus(enumKullaniciStatus.SIFRE_DEGISTIRMELI);

            KullaniciKullaniciGrup relation = new KullaniciKullaniciGrup();
            relation.setKullanici(kullanici);
            relation.setKullaniciGrup(adminGrup);
            kullanici.getKullaniciKullaniciGruplar().add(relation);

            kullaniciRepository.save(kullanici);
        }

        // 4. Menü öğeleri (önce MenuItem'ları kaydet)
        if (menuItemRepository.count() == 0) {


            MenuItem mainRoot = new MenuItem();
            mainRoot.setMenuOrder(999);
            mainRoot.setLabel("Ana Menü");
            mainRoot.setRouterLink("");
            mainRoot.setIcon("fa-solid fa-person-digging");
            menuItemRepository.save(mainRoot);

            MenuItem mUlkeler = new MenuItem();
            mUlkeler.setMenuOrder(0);
            mUlkeler.setLabel("Ülke Yönetimi");
            mUlkeler.setRouterLink("ulkeler");
            mUlkeler.setParent(mainRoot);
            mUlkeler.setIcon("fa-solid fa-globe");
            menuItemRepository.save(mUlkeler);



            MenuItem root = new MenuItem();
            root.setMenuOrder(999);
            root.setLabel("Yetkilendirme");
            root.setRouterLink("");
            menuItemRepository.save(root);

            MenuItem mYetkiler = new MenuItem();
            mYetkiler.setMenuOrder(0);
            mYetkiler.setLabel("Yetkiler");
            mYetkiler.setRouterLink("yetkiler");
            mYetkiler.setParent(root);
            mYetkiler.setIcon("fa-solid fa-drum");
            menuItemRepository.save(mYetkiler);

            MenuItem mKullaniciGruplar = new MenuItem();
            mKullaniciGruplar.setMenuOrder(1);
            mKullaniciGruplar.setLabel("Kullanıcı Gruplar");
            mKullaniciGruplar.setRouterLink("kullanici-gruplar");
            mKullaniciGruplar.setParent(root);
            mKullaniciGruplar.setIcon("fa-solid fa-people-roof");
            menuItemRepository.save(mKullaniciGruplar);

            MenuItem mKullanicilar = new MenuItem();
            mKullanicilar.setMenuOrder(2);
            mKullanicilar.setLabel("Kullanıcılar");
            mKullanicilar.setRouterLink("kullanicilar");
            mKullanicilar.setParent(root);
            mKullanicilar.setIcon("fa-solid fa-user-tie");
            menuItemRepository.save(mKullanicilar);

            MenuItem mMenuler = new MenuItem();
            mMenuler.setMenuOrder(3);
            mMenuler.setLabel("Menüler");
            mMenuler.setRouterLink("menuler");
            mMenuler.setParent(root);
            mMenuler.setIcon("fa-solid fa-bars");
            menuItemRepository.save(mMenuler);

            // 5. Yetkileri ilişkilendir (ikinci aşama)
            Yetki y1 = yetkiRepository.findByAd("YETKI_VIEW").orElseThrow();
            Yetki y2 = yetkiRepository.findByAd("KULLANICI_VIEW").orElseThrow();
            Yetki y3 = yetkiRepository.findByAd("MENU_VIEW").orElseThrow();



            MenuItemYetki mi1 = new MenuItemYetki();
            mi1.setMenuItem(mYetkiler);
            mi1.setYetki(y1);
            mYetkiler.getMenuItemYetkiler().add(mi1);
            menuItemRepository.save(mYetkiler);

            MenuItemYetki mi2 = new MenuItemYetki();
            mi2.setMenuItem(mKullaniciGruplar);
            mi2.setYetki(y2);
            mKullaniciGruplar.getMenuItemYetkiler().add(mi2);
            menuItemRepository.save(mKullaniciGruplar);

            MenuItemYetki mi3 = new MenuItemYetki();
            mi3.setMenuItem(mKullanicilar);
            mi3.setYetki(y2);
            mKullanicilar.getMenuItemYetkiler().add(mi3);
            menuItemRepository.save(mKullanicilar);

            MenuItemYetki mi4 = new MenuItemYetki();
            mi4.setMenuItem(mMenuler);
            mi4.setYetki(y3);
            mMenuler.getMenuItemYetkiler().add(mi4);
            menuItemRepository.save(mMenuler);
        }

        if(ulkeRepository.count() == 0)
        {
            ulkeRepository.save(new Ulke("Türkiye","tr"));
            ulkeRepository.save(new Ulke("Azerbaycan","az"));
            ulkeRepository.save(new Ulke("Almanya","de"));
            ulkeRepository.save(new Ulke("Amerika","us"));
            ulkeRepository.save(new Ulke("Belçika","be"));
            ulkeRepository.save(new Ulke("Japonya","jp"));
            log.info("Sample countries loaded successfully");
        }

        log.info("=== Database Seed Data Initialization Completed Successfully ===");
    }
}

