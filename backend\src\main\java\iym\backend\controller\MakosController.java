package iym.backend.controller;

import iym.backend.service.MakosApiService;
import iym.makos.api.client.gen.model.HealthCheckResponse;
import iym.makos.api.client.gen.model.IDMahkemeKararGuncellemeRequest;
import iym.makos.api.client.gen.model.ModelApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.UUID;

/**
 * MAKOS Controller
 * MAKOS API işlemlerini expose eden REST controller
 */
@RestController
@RequestMapping("/api/makos")
@RequiredArgsConstructor
@Slf4j
public class MakosController {

    private final MakosApiService makosApiService;

    /**
     * Health check endpoint
     *
     * @return Health check response
     */
    @GetMapping("/health")
    public ResponseEntity<HealthCheckResponse> healthCheck() {
        try {
            HealthCheckResponse response = makosApiService.healthCheck();
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Health check failed", e);
            var apiResponse = new ModelApiResponse();
            apiResponse.setResponseMessage(e.getMessage());
            apiResponse.setResponseCode(ModelApiResponse.ResponseCodeEnum.FAILED);
            HealthCheckResponse failedResponse = new HealthCheckResponse();
            failedResponse.setResponse(apiResponse);
            return ResponseEntity.internalServerError().body(failedResponse);
        }
    }

    /**
     * Health check endpoint with basic authorization
     *
     * @return Health check response
     */
    @GetMapping("/healthCheckAuthorized")
    public ResponseEntity<HealthCheckResponse> healthCheckAuthorized() {
        try {
            HealthCheckResponse response = makosApiService.healthCheckAuthorized();
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Health check with basic authorization failed", e);
            HealthCheckResponse failedResponse = new HealthCheckResponse();
            ModelApiResponse failedApiResponse = new ModelApiResponse();
            failedApiResponse.setResponseCode(ModelApiResponse.ResponseCodeEnum.FAILED);
            failedApiResponse.setResponseMessage(e.getMessage());
            failedResponse.setResponse(failedApiResponse);
            return ResponseEntity.internalServerError().body(failedResponse);
        }
    }

    /**
     * Mahkeme kodu güncelleme endpoint
     * File upload ile mahkeme kodu güncelleme işlemi
     *
     * @param file        Mahkeme karar dosyası
     * @param id          Request ID
     * @param mahkemeKodu Yeni mahkeme kodu
     * @param aciklama    Açıklama
     * @return Güncelleme sonucu
     */
    @PostMapping(value = "/mahkeme-kodu-guncelle", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> mahkemeKoduGuncelle(
            @RequestParam("file") MultipartFile file,
            @RequestParam("id") String id,
            @RequestParam("mahkemeKodu") String mahkemeKodu,
            @RequestParam(value = "aciklama", required = false) String aciklama) {

        try {
            log.info("Mahkeme kodu güncelleme isteği alındı - ID: {}, Mahkeme Kodu: {}", id, mahkemeKodu);

            // MultipartFile'ı File'a çevir
            File tempFile = convertMultipartFileToFile(file);

            // Request objesini oluştur
            IDMahkemeKararGuncellemeRequest request = new IDMahkemeKararGuncellemeRequest();
            request.setId(UUID.fromString(id));
            // Burada request objesinin diğer alanlarını da set edebilirsiniz
            // request.setMahkemeKodu(mahkemeKodu);
            // request.setAciklama(aciklama);

            // MAKOS API'yi çağır
            Object response = makosApiService.mahkemeKoduGuncelle(tempFile, request);

            // Temp dosyayı sil
            tempFile.delete();

            log.info("Mahkeme kodu güncelleme başarılı - ID: {}", id);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Mahkeme kodu güncelleme hatası - ID: {}", id, e);
            return ResponseEntity.internalServerError().body("Güncelleme hatası: " + e.getMessage());
        }
    }

    /**
     * MultipartFile'ı File'a çevirir
     *
     * @param multipartFile MultipartFile
     * @return File
     * @throws IOException IO hatası
     */
    private File convertMultipartFileToFile(MultipartFile multipartFile) throws IOException {
        // Temp dosya oluştur
        Path tempFile = Files.createTempFile("makos_upload_", "_" + multipartFile.getOriginalFilename());

        // MultipartFile içeriğini temp dosyaya kopyala
        Files.copy(multipartFile.getInputStream(), tempFile, StandardCopyOption.REPLACE_EXISTING);

        return tempFile.toFile();
    }
}
