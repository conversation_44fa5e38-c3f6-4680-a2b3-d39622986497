package iym.backend;

import iym.backend.config.JpaPropertiesConfig;
import iym.backend.config.PostgreSQLDataSourceConfig;
import iym.backend.postgresql.shared.config.AuditConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

import org.springframework.context.annotation.Import;
import spring.common.CommonLoader;

@SpringBootApplication(exclude = {
    DataSourceAutoConfiguration.class,
    org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration.class
})
@Import({JpaPropertiesConfig.class, AuditConfig.class, PostgreSQLDataSourceConfig.class, CommonLoader.class})
@Slf4j
public class Application {

    public static void main(String[] args) {
        try {
            SpringApplication.run(Application.class, args);
        } catch (Exception e) {
            log.error("Failed to start application", e);
        }
    }
}
