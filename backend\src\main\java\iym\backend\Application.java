package iym.backend;

import iym.backend.shared.config.AuditConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Import;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import spring.common.CommonLoader;

@SpringBootApplication
@EnableJpaRepositories(basePackages = "iym.backend")
@Import({AuditConfig.class, CommonLoader.class})
@Slf4j
public class Application {

    public static void main(String[] args) {
        try {
            SpringApplication.run(Application.class, args);
        } catch (Exception e) {
            log.error("Failed to start application", e);
        }
    }
}

