package iym.backend.makosclient.controller;

import iym.backend.makosclient.service.MakosApiService;
import iym.makos.api.client.gen.model.HealthCheckResponse;
import iym.makos.api.client.gen.model.IDMahkemeKararGuncellemeRequest;
import iym.makos.api.client.gen.model.ModelApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.UUID;

/**
 * MAKOS Controller
 * MAKOS API işlemlerini yöneten controller
 */
@RestController
@RequestMapping("/api/makos")
@RequiredArgsConstructor
@Slf4j
public class MakosController {

    private final MakosApiService makosApiService;

    /**
     * Health check endpoint
     */
    @GetMapping("/health")
    public ResponseEntity<HealthCheckResponse> healthCheck() {
        try {
            HealthCheckResponse response = makosApiService.healthCheck();
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Health check failed", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    // Diğer endpoint'ler buraya taşınacak...
    // TODO: Mevcut MakosController'dan tüm endpoint'leri buraya taşı
}
