package iym.backend.config;

import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Configuration
@EntityScan(basePackages = "iym.backend")
@EnableJpaRepositories(
    basePackages = "iym.backend",
    entityManagerFactoryRef = "postgresqlEntityManagerFactory",
    transactionManagerRef = "postgresqlTransactionManager"
)
@EnableTransactionManagement
public class PostgresqlDataSourceConfig {
    // PostgreSQL-specific configuration will be here
    // Entity scanning and repository configuration for PostgreSQL
}
