/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import iym.makos.api.client.gen.model.EvrakDetay;
import iym.makos.api.client.gen.model.MahkemeKararBilgisi;
import iym.makos.api.client.gen.model.SucTipiGuncellemeKararDetay;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Mahkeme Karar Detaylari
 */
@JsonPropertyOrder({
  IDSucTipiGuncellemeRequest.JSON_PROPERTY_ID,
  IDSucTipiGuncellemeRequest.JSON_PROPERTY_KARAR_TURU,
  IDSucTipiGuncellemeRequest.JSON_PROPERTY_EVRAK_DETAY,
  IDSucTipiGuncellemeRequest.JSON_PROPERTY_MAHKEME_KARAR_BILGISI,
  IDSucTipiGuncellemeRequest.JSON_PROPERTY_SUC_TIPI_GUNCELLEME_KARAR_DETAY_LISTESI
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class IDSucTipiGuncellemeRequest {
  public static final String JSON_PROPERTY_ID = "id";
  private UUID id;

  /**
   * Gets or Sets kararTuru
   */
  public enum KararTuruEnum {
    _0("0"),
    
    _1("1"),
    
    _2("2"),
    
    _3("3"),
    
    _4("4"),
    
    _5("5"),
    
    _6("6"),
    
    _7("7"),
    
    _8("8"),
    
    _9("9");

    private String value;

    KararTuruEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static KararTuruEnum fromValue(String value) {
      for (KararTuruEnum b : KararTuruEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_KARAR_TURU = "kararTuru";
  private KararTuruEnum kararTuru;

  public static final String JSON_PROPERTY_EVRAK_DETAY = "evrakDetay";
  private EvrakDetay evrakDetay;

  public static final String JSON_PROPERTY_MAHKEME_KARAR_BILGISI = "mahkemeKararBilgisi";
  private MahkemeKararBilgisi mahkemeKararBilgisi;

  public static final String JSON_PROPERTY_SUC_TIPI_GUNCELLEME_KARAR_DETAY_LISTESI = "sucTipiGuncellemeKararDetayListesi";
  private List<SucTipiGuncellemeKararDetay> sucTipiGuncellemeKararDetayListesi = new ArrayList<>();

  public IDSucTipiGuncellemeRequest() {
  }

  public IDSucTipiGuncellemeRequest id(UUID id) {
    
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public UUID getId() {
    return id;
  }


  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setId(UUID id) {
    this.id = id;
  }


  public IDSucTipiGuncellemeRequest kararTuru(KararTuruEnum kararTuru) {
    
    this.kararTuru = kararTuru;
    return this;
  }

   /**
   * Get kararTuru
   * @return kararTuru
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_KARAR_TURU)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public KararTuruEnum getKararTuru() {
    return kararTuru;
  }


  @JsonProperty(JSON_PROPERTY_KARAR_TURU)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setKararTuru(KararTuruEnum kararTuru) {
    this.kararTuru = kararTuru;
  }


  public IDSucTipiGuncellemeRequest evrakDetay(EvrakDetay evrakDetay) {
    
    this.evrakDetay = evrakDetay;
    return this;
  }

   /**
   * Get evrakDetay
   * @return evrakDetay
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_EVRAK_DETAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public EvrakDetay getEvrakDetay() {
    return evrakDetay;
  }


  @JsonProperty(JSON_PROPERTY_EVRAK_DETAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setEvrakDetay(EvrakDetay evrakDetay) {
    this.evrakDetay = evrakDetay;
  }


  public IDSucTipiGuncellemeRequest mahkemeKararBilgisi(MahkemeKararBilgisi mahkemeKararBilgisi) {
    
    this.mahkemeKararBilgisi = mahkemeKararBilgisi;
    return this;
  }

   /**
   * Get mahkemeKararBilgisi
   * @return mahkemeKararBilgisi
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_BILGISI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public MahkemeKararBilgisi getMahkemeKararBilgisi() {
    return mahkemeKararBilgisi;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_BILGISI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMahkemeKararBilgisi(MahkemeKararBilgisi mahkemeKararBilgisi) {
    this.mahkemeKararBilgisi = mahkemeKararBilgisi;
  }


  public IDSucTipiGuncellemeRequest sucTipiGuncellemeKararDetayListesi(List<SucTipiGuncellemeKararDetay> sucTipiGuncellemeKararDetayListesi) {
    
    this.sucTipiGuncellemeKararDetayListesi = sucTipiGuncellemeKararDetayListesi;
    return this;
  }

  public IDSucTipiGuncellemeRequest addSucTipiGuncellemeKararDetayListesiItem(SucTipiGuncellemeKararDetay sucTipiGuncellemeKararDetayListesiItem) {
    if (this.sucTipiGuncellemeKararDetayListesi == null) {
      this.sucTipiGuncellemeKararDetayListesi = new ArrayList<>();
    }
    this.sucTipiGuncellemeKararDetayListesi.add(sucTipiGuncellemeKararDetayListesiItem);
    return this;
  }

   /**
   * Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek aidiyat bilgileri
   * @return sucTipiGuncellemeKararDetayListesi
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_SUC_TIPI_GUNCELLEME_KARAR_DETAY_LISTESI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public List<SucTipiGuncellemeKararDetay> getSucTipiGuncellemeKararDetayListesi() {
    return sucTipiGuncellemeKararDetayListesi;
  }


  @JsonProperty(JSON_PROPERTY_SUC_TIPI_GUNCELLEME_KARAR_DETAY_LISTESI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setSucTipiGuncellemeKararDetayListesi(List<SucTipiGuncellemeKararDetay> sucTipiGuncellemeKararDetayListesi) {
    this.sucTipiGuncellemeKararDetayListesi = sucTipiGuncellemeKararDetayListesi;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    IDSucTipiGuncellemeRequest idSucTipiGuncellemeRequest = (IDSucTipiGuncellemeRequest) o;
    return Objects.equals(this.id, idSucTipiGuncellemeRequest.id) &&
        Objects.equals(this.kararTuru, idSucTipiGuncellemeRequest.kararTuru) &&
        Objects.equals(this.evrakDetay, idSucTipiGuncellemeRequest.evrakDetay) &&
        Objects.equals(this.mahkemeKararBilgisi, idSucTipiGuncellemeRequest.mahkemeKararBilgisi) &&
        Objects.equals(this.sucTipiGuncellemeKararDetayListesi, idSucTipiGuncellemeRequest.sucTipiGuncellemeKararDetayListesi);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, kararTuru, evrakDetay, mahkemeKararBilgisi, sucTipiGuncellemeKararDetayListesi);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class IDSucTipiGuncellemeRequest {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    kararTuru: ").append(toIndentedString(kararTuru)).append("\n");
    sb.append("    evrakDetay: ").append(toIndentedString(evrakDetay)).append("\n");
    sb.append("    mahkemeKararBilgisi: ").append(toIndentedString(mahkemeKararBilgisi)).append("\n");
    sb.append("    sucTipiGuncellemeKararDetayListesi: ").append(toIndentedString(sucTipiGuncellemeKararDetayListesi)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

