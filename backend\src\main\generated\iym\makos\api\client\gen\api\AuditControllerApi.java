package iym.makos.api.client.gen.api;

import iym.makos.api.client.gen.handler.ApiClient;

import iym.makos.api.client.gen.model.GetMakosUserAuditLogListResponse;
import iym.makos.api.client.gen.model.GetMakosUserAuditLogResponse;
import iym.makos.api.client.gen.model.GetMakosUserAuditLogsByTypeResponse;
import iym.makos.api.client.gen.model.GetMakosUserAuditLogsByUsernameResponse;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class AuditControllerApi {
    private ApiClient apiClient;

    public AuditControllerApi() {
        this(new ApiClient());
    }

    public AuditControllerApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param id  (required)
     * @return GetMakosUserAuditLogResponse
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public GetMakosUserAuditLogResponse getMakosUserAuditLog(Long id) throws RestClientException {
        return getMakosUserAuditLogWithHttpInfo(id).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param id  (required)
     * @return ResponseEntity&lt;GetMakosUserAuditLogResponse&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<GetMakosUserAuditLogResponse> getMakosUserAuditLogWithHttpInfo(Long id) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'id' is set
        if (id == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'id' when calling getMakosUserAuditLog");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("id", id);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "*/*"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "BasicAuth" };

        ParameterizedTypeReference<GetMakosUserAuditLogResponse> localReturnType = new ParameterizedTypeReference<GetMakosUserAuditLogResponse>() {};
        return apiClient.invokeAPI("/audit/getMakosUserAuditLog/{id}", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param page  (optional, default to 0)
     * @param count  (optional, default to 200)
     * @param sortBy  (optional, default to requestTime)
     * @param sortDirection  (optional, default to desc)
     * @return GetMakosUserAuditLogListResponse
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public GetMakosUserAuditLogListResponse getMakosUserAuditLogList(Integer page, Integer count, String sortBy, String sortDirection) throws RestClientException {
        return getMakosUserAuditLogListWithHttpInfo(page, count, sortBy, sortDirection).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param page  (optional, default to 0)
     * @param count  (optional, default to 200)
     * @param sortBy  (optional, default to requestTime)
     * @param sortDirection  (optional, default to desc)
     * @return ResponseEntity&lt;GetMakosUserAuditLogListResponse&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<GetMakosUserAuditLogListResponse> getMakosUserAuditLogListWithHttpInfo(Integer page, Integer count, String sortBy, String sortDirection) throws RestClientException {
        Object localVarPostBody = null;
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "page", page));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "count", count));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "sortBy", sortBy));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "sortDirection", sortDirection));


        final String[] localVarAccepts = { 
            "*/*"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "BasicAuth" };

        ParameterizedTypeReference<GetMakosUserAuditLogListResponse> localReturnType = new ParameterizedTypeReference<GetMakosUserAuditLogListResponse>() {};
        return apiClient.invokeAPI("/audit/getMakosUserAuditLogList", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param auditType  (required)
     * @param page  (optional, default to 0)
     * @param count  (optional, default to 200)
     * @param sortBy  (optional, default to requestTime)
     * @param sortDirection  (optional, default to desc)
     * @return GetMakosUserAuditLogsByTypeResponse
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public GetMakosUserAuditLogsByTypeResponse getMakosUserAuditLogsByType(String auditType, Integer page, Integer count, String sortBy, String sortDirection) throws RestClientException {
        return getMakosUserAuditLogsByTypeWithHttpInfo(auditType, page, count, sortBy, sortDirection).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param auditType  (required)
     * @param page  (optional, default to 0)
     * @param count  (optional, default to 200)
     * @param sortBy  (optional, default to requestTime)
     * @param sortDirection  (optional, default to desc)
     * @return ResponseEntity&lt;GetMakosUserAuditLogsByTypeResponse&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<GetMakosUserAuditLogsByTypeResponse> getMakosUserAuditLogsByTypeWithHttpInfo(String auditType, Integer page, Integer count, String sortBy, String sortDirection) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'auditType' is set
        if (auditType == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'auditType' when calling getMakosUserAuditLogsByType");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "auditType", auditType));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "page", page));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "count", count));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "sortBy", sortBy));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "sortDirection", sortDirection));


        final String[] localVarAccepts = { 
            "*/*"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "BasicAuth" };

        ParameterizedTypeReference<GetMakosUserAuditLogsByTypeResponse> localReturnType = new ParameterizedTypeReference<GetMakosUserAuditLogsByTypeResponse>() {};
        return apiClient.invokeAPI("/audit/getMakosUserAuditLogsByType", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param username  (required)
     * @param page  (optional, default to 0)
     * @param count  (optional, default to 200)
     * @param sortBy  (optional, default to requestTime)
     * @param sortDirection  (optional, default to desc)
     * @return GetMakosUserAuditLogsByUsernameResponse
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public GetMakosUserAuditLogsByUsernameResponse getMakosUserAuditLogsByUsername(String username, Integer page, Integer count, String sortBy, String sortDirection) throws RestClientException {
        return getMakosUserAuditLogsByUsernameWithHttpInfo(username, page, count, sortBy, sortDirection).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param username  (required)
     * @param page  (optional, default to 0)
     * @param count  (optional, default to 200)
     * @param sortBy  (optional, default to requestTime)
     * @param sortDirection  (optional, default to desc)
     * @return ResponseEntity&lt;GetMakosUserAuditLogsByUsernameResponse&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<GetMakosUserAuditLogsByUsernameResponse> getMakosUserAuditLogsByUsernameWithHttpInfo(String username, Integer page, Integer count, String sortBy, String sortDirection) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'username' is set
        if (username == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'username' when calling getMakosUserAuditLogsByUsername");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "username", username));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "page", page));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "count", count));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "sortBy", sortBy));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "sortDirection", sortDirection));


        final String[] localVarAccepts = { 
            "*/*"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "BasicAuth" };

        ParameterizedTypeReference<GetMakosUserAuditLogsByUsernameResponse> localReturnType = new ParameterizedTypeReference<GetMakosUserAuditLogsByUsernameResponse>() {};
        return apiClient.invokeAPI("/audit/getMakosUserAuditLogsByUsername", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
}
