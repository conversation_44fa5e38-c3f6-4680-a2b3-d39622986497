package iym.makos.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import org.springframework.http.HttpStatus;

public enum MakosResponseCode {

    SUCCESS,
    INVALID_REQUEST,

    FAILED;


    @JsonCreator
    public static MakosResponseCode fromName(String name) {
        for (MakosResponseCode code : MakosResponseCode.values()) {
            if (code.name().equals(name)) {
                return code;
            }
        }
        throw new IllegalArgumentException("Invalid code: '" + name + "'");
    }

    public static HttpStatus toHttpStatus(MakosResponseCode makosResponseCode) {
        switch (makosResponseCode) {
            case SUCCESS -> {
                return HttpStatus.OK;
            }
            case FAILED -> {
                return HttpStatus.INTERNAL_SERVER_ERROR;
            }
            case INVALID_REQUEST -> {
                return HttpStatus.BAD_REQUEST;
            }
        }
        return HttpStatus.OK;
    }
}

