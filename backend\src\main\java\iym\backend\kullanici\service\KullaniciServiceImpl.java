package iym.backend.kullanici.service;

import lombok.RequiredArgsConstructor;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import iym.backend.authentication.dto.ChangePasswordRequest;
import iym.backend.authentication.dto.JwtResponse;
import iym.backend.kullanici.dto.KullaniciDto;
import iym.backend.kullanici.entity.Kullanici;
import iym.backend.kullanici.enums.enumKullaniciStatus;
import iym.backend.kullanici.mapper.KullaniciMapper;
import iym.backend.kullanici.repository.KullaniciRepository;
import iym.backend.kullanicigrup.entity.KullaniciGrup;
import iym.backend.kullanicigrup.repository.KullaniciGrupRepository;
import iym.backend.shared.service.BaseServiceImpl;

import java.util.List;

@Service
public class KullaniciServiceImpl extends BaseServiceImpl<Kullanici, KullaniciDto, Long>
        implements KullaniciService {

    private final KullaniciRepository repository;
    private final KullaniciMapper mapper;
    private final PasswordEncoder passwordEncoder;
    private final KullaniciGrupRepository kullaniciGrupRepository;

    public KullaniciServiceImpl(
            KullaniciRepository repository,
            KullaniciMapper mapper,
            PasswordEncoder passwordEncoder,
            KullaniciGrupRepository kullaniciGrupRepository
    ) {
        super(repository, mapper);
        this.repository = repository;
        this.mapper = mapper;
        this.passwordEncoder = passwordEncoder;
        this.kullaniciGrupRepository = kullaniciGrupRepository;
    }

    @Override
    public KullaniciDto save(KullaniciDto dto) {
        List<KullaniciGrup> gruplar = kullaniciGrupRepository.findAllById(dto.getKullaniciGrupIdList());

        Kullanici entity = mapper.toEntity(dto);
        mapper.mapKullaniciGruplar(dto, entity, gruplar);

        // Set status from DTO if provided, otherwise use default
        if (dto.getStatus() != null && !dto.getStatus().isBlank()) {
            entity.setStatus(enumKullaniciStatus.valueOf(dto.getStatus()));
        } else {
            entity.setStatus(enumKullaniciStatus.SIFRE_DEGISTIRMELI);
        }

        if (entity.getParola() == null || entity.getParola().isBlank()) {
            entity.setParola(passwordEncoder.encode("1"));
        } else {
            entity.setParola(passwordEncoder.encode(entity.getParola()));
        }

        return mapper.toDto(repository.save(entity));
    }

    public JwtResponse changePassword(ChangePasswordRequest request) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = (String) authentication.getPrincipal(); // sadece username ve password içerir

       // String username = userDetails.getUsername();

        Kullanici kullanici = repository.findByKullaniciAdi(username)
                .orElseThrow(() -> new RuntimeException("Kullanıcı bulunamadı"));

        // Mevcut parola kontrolü
        if (!passwordEncoder.matches(request.getCurrentPassword(), kullanici.getParola())) {
            throw new RuntimeException("Mevcut parola hatalı");
        }

        // Yeni parolalar eşleşiyor mu?
        if (!request.getNewPassword().equals(request.getNewPassword2())) {
            throw new RuntimeException("Yeni parolalar aynı değil");
        }

        // Parolayı güncelle

        kullanici.setStatus(enumKullaniciStatus.AKTIF);
        kullanici.setParola(passwordEncoder.encode(request.getNewPassword()));
        repository.save(kullanici);

        return new JwtResponse("Parola başarıyla güncellendi",enumKullaniciStatus.AKTIF.name());
    }
}

