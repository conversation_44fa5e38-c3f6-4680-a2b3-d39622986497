package iym.backend.kullanici.repository;

import iym.backend.kullanici.entity.Kullanici;
import iym.backend.shared.repository.BaseRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface KullaniciRepository extends BaseRepository<<PERSON><PERSON><PERSON>, Long> {
    Optional<Kullanici> findByKullaniciAdi(String kullaniciAdi);


}

