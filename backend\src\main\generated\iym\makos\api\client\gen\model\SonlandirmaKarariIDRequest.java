/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import iym.makos.api.client.gen.model.IDSonlandirmaKarariRequest;
import java.io.File;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * SonlandirmaKarariIDRequest
 */
@JsonPropertyOrder({
  SonlandirmaKarariIDRequest.JSON_PROPERTY_MAHKEME_KARAR_DOSYASI_I_D,
  SonlandirmaKarariIDRequest.JSON_PROPERTY_MAHKEME_KARAR_DETAY_I_D
})
@JsonTypeName("sonlandirmaKarariID_request")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class SonlandirmaKarariIDRequest {
  public static final String JSON_PROPERTY_MAHKEME_KARAR_DOSYASI_I_D = "mahkemeKararDosyasiID";
  private File mahkemeKararDosyasiID;

  public static final String JSON_PROPERTY_MAHKEME_KARAR_DETAY_I_D = "mahkemeKararDetayID";
  private IDSonlandirmaKarariRequest mahkemeKararDetayID;

  public SonlandirmaKarariIDRequest() {
  }

  public SonlandirmaKarariIDRequest mahkemeKararDosyasiID(File mahkemeKararDosyasiID) {
    
    this.mahkemeKararDosyasiID = mahkemeKararDosyasiID;
    return this;
  }

   /**
   * Get mahkemeKararDosyasiID
   * @return mahkemeKararDosyasiID
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DOSYASI_I_D)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public File getMahkemeKararDosyasiID() {
    return mahkemeKararDosyasiID;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DOSYASI_I_D)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMahkemeKararDosyasiID(File mahkemeKararDosyasiID) {
    this.mahkemeKararDosyasiID = mahkemeKararDosyasiID;
  }


  public SonlandirmaKarariIDRequest mahkemeKararDetayID(IDSonlandirmaKarariRequest mahkemeKararDetayID) {
    
    this.mahkemeKararDetayID = mahkemeKararDetayID;
    return this;
  }

   /**
   * Get mahkemeKararDetayID
   * @return mahkemeKararDetayID
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DETAY_I_D)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public IDSonlandirmaKarariRequest getMahkemeKararDetayID() {
    return mahkemeKararDetayID;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DETAY_I_D)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMahkemeKararDetayID(IDSonlandirmaKarariRequest mahkemeKararDetayID) {
    this.mahkemeKararDetayID = mahkemeKararDetayID;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SonlandirmaKarariIDRequest sonlandirmaKarariIDRequest = (SonlandirmaKarariIDRequest) o;
    return Objects.equals(this.mahkemeKararDosyasiID, sonlandirmaKarariIDRequest.mahkemeKararDosyasiID) &&
        Objects.equals(this.mahkemeKararDetayID, sonlandirmaKarariIDRequest.mahkemeKararDetayID);
  }

  @Override
  public int hashCode() {
    return Objects.hash(mahkemeKararDosyasiID, mahkemeKararDetayID);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SonlandirmaKarariIDRequest {\n");
    sb.append("    mahkemeKararDosyasiID: ").append(toIndentedString(mahkemeKararDosyasiID)).append("\n");
    sb.append("    mahkemeKararDetayID: ").append(toIndentedString(mahkemeKararDetayID)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

