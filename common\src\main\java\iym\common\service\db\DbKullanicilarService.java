package iym.common.service.db;


import iym.common.model.entity.iym.Kullanicilar;

import java.util.List;
import java.util.Optional;

public interface DbKullanicilarService extends GenericDbService<Kullanicilar, Long> {

    /**
     * Kullanıcı adına göre kullanıcı bulma
     * @param kullaniciAdi Kullanıcı adı
     * @return Bulunan kullanıcı
     */
    Optional<Kullanicilar> findByKullaniciAdi(String kullaniciAdi);

    /**
     * Duruma göre kullanıcıları bulma
     * @param durumu Kullanıcı durumu
     * @return Bulunan kullanıcılar
     */
    List<Kullanicilar> findByDurumu(String durumu);

    /**
     * Tüm kullanıcıları kullanıcı adına göre sıralı getirme
     * @return Sıralı kullanıcı listesi
     */
    List<Kullanicilar> findAllByOrderByKullaniciAdiAsc();
}
