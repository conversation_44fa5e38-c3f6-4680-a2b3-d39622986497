package iym.makos.handler;

import iym.common.model.entity.iym.*;
import iym.common.util.CommonUtils;
import iym.db.jpa.dao.*;
import iym.makos.dto.id.IDMahkemeKararGuncellemeRequest;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
public class IDMahkemeKararGuncellemeDBSaveHandler extends MahkemeKararRequestDbSaveHandlerBase<IDMahkemeKararGuncellemeRequest> {

    private MahkemeKararRepo mahkemeKararRepo;
    private MahkemeKararTalepRepo mahkemeKararTalepRepo;
    private DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo;
    private HedeflerTalepRepo hedeflerTalepRepo;
    private HedeflerRepo hedeflerRepo;
    private HedeflerAidiyatTalepRepo hedeflerAidiyatTalepRepo;
    private KararRequestMapper kararRequestMapper;
    private MahkemeKararBilgiGuncelleTalepRepo mahkemeKararBilgiGuncelleTalepRepo;

    @Autowired
    public IDMahkemeKararGuncellemeDBSaveHandler(MahkemeKararRepo mahkemeKararRepo
            , MahkemeKararTalepRepo mahkemeKararTalepRepo
            , DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo
            , HedeflerRepo hedeflerRepo
            , HedeflerTalepRepo hedeflerTalepRepo
            , HedeflerAidiyatTalepRepo hedeflerAidiyatTalepRepo
            , KararRequestMapper kararRequestMapper
            , MahkemeKararBilgiGuncelleTalepRepo mahkemeKararBilgiGuncelleTalepRepo) {
        this.mahkemeKararRepo = mahkemeKararRepo;
        this.mahkemeKararTalepRepo = mahkemeKararTalepRepo;
        this.dMahkemeKararTalepRepo = dMahkemeKararTalepRepo;
        this.hedeflerRepo = hedeflerRepo;
        this.hedeflerTalepRepo = hedeflerTalepRepo;
        this.hedeflerAidiyatTalepRepo = hedeflerAidiyatTalepRepo;
        this.kararRequestMapper = kararRequestMapper;
        this.mahkemeKararBilgiGuncelleTalepRepo = mahkemeKararBilgiGuncelleTalepRepo;
    }

    @Override
    @Transactional
    public Long kaydet(IDMahkemeKararGuncellemeRequest request, Date kayitTarihi, Long kullaniciId) throws Exception {
        try {
            Long mahkemeKararTalepId = mahkemeKararRequestCommonDbSaver.handleDbSave(request, kayitTarihi, kullaniciId);
            Long evrakId = null;
            Optional<MahkemeKararTalep> mahkemeKararTalepOpt = mahkemeKararTalepRepo.findById(mahkemeKararTalepId);
            if (mahkemeKararTalepOpt.isEmpty()) {
                throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARARTALEP_KAYDETMEHATASI);
            } else {
                evrakId = mahkemeKararTalepOpt.get().getEvrakId();
            }

            for (MahkemeKararGuncellemeDetay mahkemeKararGuncellemeDetay : request.getMahkemeKararGuncellemeDetayListesi()) {

                //Güncellemeye konu mahkeme karari bul
                MahkemeKararDetay ilgiliMahhemeKararDetay = mahkemeKararGuncellemeDetay.getMahkemeKararDetay();
                Optional<MahkemeKarar> mahkemeKararOpt = mahkemeKararRepo.findBy(ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKararNo()
                        , ilgiliMahhemeKararDetay.getSorusturmaNo());
                if (mahkemeKararOpt.isEmpty()) {
                    String errorStr = String.format(MakosResponseErrorCodes.MK_BULUNAMADI, ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                            , ilgiliMahhemeKararDetay.getMahkemeKodu(), ilgiliMahhemeKararDetay.getMahkemeKararNo()
                            , ilgiliMahhemeKararDetay.getSorusturmaNo());
                    throw new MakosResponseException(errorStr);
                } else {
                    MahkemeKarar iliskiliMahkemeKarar = mahkemeKararOpt.get();

                    DetayMahkemeKararTalep dMahkemeKararTalep = kararRequestMapper.toDMahkemeKararTalep(iliskiliMahkemeKarar, mahkemeKararTalepId, evrakId);
                    DetayMahkemeKararTalep savedDMahkemeKararTalep = dMahkemeKararTalepRepo.save(dMahkemeKararTalep);
                    if(savedDMahkemeKararTalep == null){
                        throw new MakosResponseException(MakosResponseErrorCodes.MKTALEP_DETAY_KAYIT_HATASI);
                    }

                    String yeniMahkemeKodu = "";
                    String yeniSorusturmaNo = "";
                    String yeniMahkemeKararNo = "";

                    for(MahkemeKararGuncellemeBilgi mahkemeKararGuncellemeBilgi : mahkemeKararGuncellemeDetay.getMahkemeKararGuncellemeBilgiListesi()){
                        if(mahkemeKararGuncellemeBilgi.getMahkemeKararGuncellemeAlan() == MahkemeKararGuncellemeAlan.MAHKEME_KODU){
                            yeniMahkemeKodu = mahkemeKararGuncellemeBilgi.getYeniDegeri();
                             }
                        else if(mahkemeKararGuncellemeBilgi.getMahkemeKararGuncellemeAlan() == MahkemeKararGuncellemeAlan.MAHKEMEKARAR_NO){
                            yeniMahkemeKararNo = mahkemeKararGuncellemeBilgi.getYeniDegeri();

                        }
                        else if(mahkemeKararGuncellemeBilgi.getMahkemeKararGuncellemeAlan() == MahkemeKararGuncellemeAlan.SORUSTURMA_NO){
                            yeniSorusturmaNo = mahkemeKararGuncellemeBilgi.getYeniDegeri();
                        }
                    }



                    if(CommonUtils.isNullOrEmpty(yeniMahkemeKodu) && CommonUtils.isNullOrEmpty(yeniSorusturmaNo) & CommonUtils.isNullOrEmpty(yeniMahkemeKararNo)){
                        throw new MakosResponseException("mahkemeKodu, soruşturma numarası veya  makhkeme karar numaralarımdan en az bir tanesi belirtilmeldir.");
                    }

                    String updateColumnNames = mahkemeKararGuncellemeDetay.getMahkemeKararGuncellemeBilgiListesi().stream()
                            .map(guncellemeBilgi -> guncellemeBilgi.getMahkemeKararGuncellemeAlan().name())
                            .collect(Collectors.joining(","));


                    MahkemeKararBilgiGuncellemeTalep mahkemeKararBilgiGuncellemeTalep = new MahkemeKararBilgiGuncellemeTalep();
                    mahkemeKararBilgiGuncellemeTalep.setMahkemeKararDetayId(dMahkemeKararTalep.getId());
                    mahkemeKararBilgiGuncellemeTalep.setMahkemeKodu(yeniMahkemeKodu);
                    mahkemeKararBilgiGuncellemeTalep.setSorusturmaNo(yeniSorusturmaNo);
                    mahkemeKararBilgiGuncellemeTalep.setMahkemeKararNo(yeniMahkemeKararNo);
                    mahkemeKararBilgiGuncellemeTalep.setUpdateColumnNames(updateColumnNames);

                    MahkemeKararBilgiGuncellemeTalep savedMahkemeKararBilgiGuncellemeTalep = mahkemeKararBilgiGuncelleTalepRepo.save(mahkemeKararBilgiGuncellemeTalep);
                    if(savedMahkemeKararBilgiGuncellemeTalep == null){
                        throw new MakosResponseException(MakosResponseErrorCodes.MKTALEP_MAHKEMEBILGIGUNCELLEME_KAYIT_HATASI);
                    }
                }
            }

            return mahkemeKararTalepId;
        } catch (Exception ex) {
            log.error("IDMahkemeKararGuncelleme handleDbSave failed. id:{}, evrakNo:{}", request.getId(), request.getEvrakDetay().getEvrakNo(), ex);
            throw new RuntimeException(ex);
        }
    }

}

