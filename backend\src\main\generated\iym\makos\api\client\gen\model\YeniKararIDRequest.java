/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import iym.makos.api.client.gen.model.IDYeniKararRequest;
import java.io.File;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * YeniKararIDRequest
 */
@JsonPropertyOrder({
  YeniKararIDRequest.JSON_PROPERTY_MAHKEME_KARAR_DOSYASI_I_D,
  YeniKararIDRequest.JSON_PROPERTY_JSON_DATA
})
@JsonTypeName("yeniKararID_request")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class YeniKararIDRequest {
  public static final String JSON_PROPERTY_MAHKEME_KARAR_DOSYASI_I_D = "mahkemeKararDosyasiID";
  private File mahkemeKararDosyasiID;

  public static final String JSON_PROPERTY_JSON_DATA = "jsonData";
  private IDYeniKararRequest jsonData;

  public YeniKararIDRequest() {
  }

  public YeniKararIDRequest mahkemeKararDosyasiID(File mahkemeKararDosyasiID) {
    
    this.mahkemeKararDosyasiID = mahkemeKararDosyasiID;
    return this;
  }

   /**
   * Get mahkemeKararDosyasiID
   * @return mahkemeKararDosyasiID
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DOSYASI_I_D)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public File getMahkemeKararDosyasiID() {
    return mahkemeKararDosyasiID;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DOSYASI_I_D)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMahkemeKararDosyasiID(File mahkemeKararDosyasiID) {
    this.mahkemeKararDosyasiID = mahkemeKararDosyasiID;
  }


  public YeniKararIDRequest jsonData(IDYeniKararRequest jsonData) {
    
    this.jsonData = jsonData;
    return this;
  }

   /**
   * Get jsonData
   * @return jsonData
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_JSON_DATA)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public IDYeniKararRequest getJsonData() {
    return jsonData;
  }


  @JsonProperty(JSON_PROPERTY_JSON_DATA)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setJsonData(IDYeniKararRequest jsonData) {
    this.jsonData = jsonData;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    YeniKararIDRequest yeniKararIDRequest = (YeniKararIDRequest) o;
    return Objects.equals(this.mahkemeKararDosyasiID, yeniKararIDRequest.mahkemeKararDosyasiID) &&
        Objects.equals(this.jsonData, yeniKararIDRequest.jsonData);
  }

  @Override
  public int hashCode() {
    return Objects.hash(mahkemeKararDosyasiID, jsonData);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class YeniKararIDRequest {\n");
    sb.append("    mahkemeKararDosyasiID: ").append(toIndentedString(mahkemeKararDosyasiID)).append("\n");
    sb.append("    jsonData: ").append(toIndentedString(jsonData)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

