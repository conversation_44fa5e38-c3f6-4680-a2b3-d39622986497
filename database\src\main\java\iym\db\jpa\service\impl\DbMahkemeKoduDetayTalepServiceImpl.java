package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.MahkemeKararBilgiGuncellemeTalep;
import iym.common.service.db.DbMahkemeKoduDetayTalepService;
import iym.db.jpa.dao.MahkemeKararBilgiGuncelleTalepRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * Service implementation for MahkemeKararTalep entity
 */
@Service
public class DbMahkemeKoduDetayTalepServiceImpl extends GenericDbServiceImpl<MahkemeKararBilgiGuncellemeTalep, Long> implements DbMahkemeKoduDetayTalepService {

    private final MahkemeKararBilgiGuncelleTalepRepo mahkemeKararBilgiGuncelleTalepRepo;

    @Autowired
    public DbMahkemeKoduDetayTalepServiceImpl(MahkemeKararBilgiGuncelleTalepRepo repository) {
        super(repository);
        this.mahkemeKararBilgiGuncelleTalepRepo = repository;
    }

    @Override
    public Optional<MahkemeKararBilgiGuncellemeTalep> findByMahkemeKararDetayId(Long mahkemeKararDetayId){
        return mahkemeKararBilgiGuncelleTalepRepo.findByMahkemeKararDetayId(mahkemeKararDetayId);
    }
}
