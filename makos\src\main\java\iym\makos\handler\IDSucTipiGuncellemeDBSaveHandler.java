package iym.makos.handler;

import iym.common.model.api.GuncellemeTip;
import iym.common.model.entity.iym.*;
import iym.common.util.CommonUtils;
import iym.db.jpa.dao.*;
import iym.makos.dto.id.IDSucTipiGuncellemeRequest;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class IDSucTipiGuncellemeDBSaveHandler extends MahkemeKararRequestDbSaveHandlerBase<IDSucTipiGuncellemeRequest> {

    private MahkemeKararRepo mahkemeKararRepo;
    private MahkemeKararSucTipiRepo mahkemeKararSucTipiRepo;
    private MahkemeKararTalepRepo mahkemeKararTalepRepo;
    private HedeflerAidiyatTalepRepo hedeflerAidiyatTalepRepo;
    private KararRequestMapper kararRequestMapper;
    private MahkemeSucTipiDetayTalepRepo mahkemeSucTipiDetayTalepRepo;
    private DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo;
    private MahkemeAidiyatRepo mahkemeAidiyatRepo;

    @Autowired
    public IDSucTipiGuncellemeDBSaveHandler(MahkemeKararRepo mahkemeKararRepo
            , MahkemeKararSucTipiRepo mahkemeKararSucTipiRepo
            , MahkemeKararTalepRepo mahkemeKararTalepRepo
            , HedeflerAidiyatTalepRepo hedeflerAidiyatTalepRepo
            , DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo
            , MahkemeSucTipiDetayTalepRepo mahkemeSucTipiDetayTalepRepo
            , KararRequestMapper kararRequestMapper
            , MahkemeAidiyatRepo mahkemeAidiyatRepo) {
        this.mahkemeKararRepo = mahkemeKararRepo;
        this.mahkemeKararSucTipiRepo = mahkemeKararSucTipiRepo;
        this.mahkemeKararTalepRepo = mahkemeKararTalepRepo;
        this.hedeflerAidiyatTalepRepo = hedeflerAidiyatTalepRepo;
        this.kararRequestMapper = kararRequestMapper;
        this.dMahkemeKararTalepRepo = dMahkemeKararTalepRepo;
        this.mahkemeSucTipiDetayTalepRepo = mahkemeSucTipiDetayTalepRepo;
        this.mahkemeAidiyatRepo = mahkemeAidiyatRepo;
    }

    @Override
    @Transactional
    public Long kaydet(IDSucTipiGuncellemeRequest request, Date kayitTarihi, Long kullaniciId) throws Exception {
        try {
            Long mahkemeKararTalepId = mahkemeKararRequestCommonDbSaver.handleDbSave(request, kayitTarihi, kullaniciId);
            Long evrakId;
            Optional<MahkemeKararTalep> mahkemeKararTalepOpt = mahkemeKararTalepRepo.findById(mahkemeKararTalepId);
            if (mahkemeKararTalepOpt.isEmpty()) {
                throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARARTALEP_KAYDETMEHATASI);
            } else {
                evrakId = mahkemeKararTalepOpt.get().getEvrakId();
            }
            List<SucTipiGuncellemeKararDetay> guncellemeListesi = request.getSucTipiGuncellemeKararDetayListesi();

            for (SucTipiGuncellemeKararDetay guncellemeBilgisi : guncellemeListesi) {
                //Güncellemeye konu mahkeme karari bul
                MahkemeKararDetay ilgiliMahhemeKararDetay = guncellemeBilgisi.getMahkemeKararDetay();
                Optional<MahkemeKarar> mahkemeKararOpt = mahkemeKararRepo.findBy(ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKararNo()
                        , ilgiliMahhemeKararDetay.getSorusturmaNo());
                if (mahkemeKararOpt.isEmpty()) {
                    String errorStr = String.format(MakosResponseErrorCodes.MK_BULUNAMADI, ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                            , ilgiliMahhemeKararDetay.getMahkemeKodu(), ilgiliMahhemeKararDetay.getMahkemeKararNo()
                            , ilgiliMahhemeKararDetay.getSorusturmaNo());
                    throw new MakosResponseException(errorStr);
                }
                MahkemeKarar iliskiliMahkemeKarar = mahkemeKararOpt.get();


                DetayMahkemeKararTalep dMahkemeKararTalep = kararRequestMapper.toDMahkemeKararTalep(iliskiliMahkemeKarar, mahkemeKararTalepId, evrakId);
                DetayMahkemeKararTalep savedDMahkemeKararTalep = dMahkemeKararTalepRepo.save(dMahkemeKararTalep);

                for (SucTipiGuncellemeDetay sucTipiGuncellemeDetay : CommonUtils.safeList( guncellemeBilgisi.getSucTipiGuncellemeDetayListesi())) {

                    GuncellemeTip guncellemeTip = sucTipiGuncellemeDetay.getGuncellemeTip();
                    String sucTipiKodu = sucTipiGuncellemeDetay.getSucTipiKodu();

                    Optional<MahkemeKararSuclar> mahkemeKararSucOpt = mahkemeKararSucTipiRepo.findByMahkemeKararIdAndSucTipKodu(iliskiliMahkemeKarar.getId(), sucTipiKodu);
                    if (guncellemeTip == GuncellemeTip.EKLE && !mahkemeKararSucOpt.isEmpty() ) {
                        throw new Exception( String.format( MakosResponseErrorCodes.MK_SUCTIPI_ZATENVAR, iliskiliMahkemeKarar.getId(), sucTipiKodu));
                    } else if (guncellemeTip == GuncellemeTip.CIKAR && mahkemeKararSucOpt.isEmpty()) {
                        throw new Exception( String.format( MakosResponseErrorCodes.MK_SUCTIPI_BULUNAMADI, iliskiliMahkemeKarar.getId(), sucTipiKodu));
                    }

                    MahkemeSucTipiDetayTalep mahkemeSucTipiDetayTalep = new MahkemeSucTipiDetayTalep();
                    mahkemeSucTipiDetayTalep.setMahkemeKararDetayTalepId(savedDMahkemeKararTalep.getId());
                    mahkemeSucTipiDetayTalep.setMahkemeKararTalepId(mahkemeKararTalepId);
                    mahkemeSucTipiDetayTalep.setIliskiliMahkemeKararId(iliskiliMahkemeKarar.getId());
                    mahkemeSucTipiDetayTalep.setTarih(kayitTarihi);
                    if (guncellemeTip == GuncellemeTip.EKLE) {
                        mahkemeSucTipiDetayTalep.setMahkemeSucTipiKoduEkle(sucTipiKodu);
                    } else {
                        mahkemeSucTipiDetayTalep.setMahkemeSucTipiKoduCikar(sucTipiKodu);
                    }
                    MahkemeSucTipiDetayTalep savedMahkemeAidiyatDetayTalep = mahkemeSucTipiDetayTalepRepo.save(mahkemeSucTipiDetayTalep);
                }
            }


            return mahkemeKararTalepId;
        } catch (Exception ex) {
            log.error("IDAidiyatBilgisiGuncelleme handleDbSave failed. id:{}, evrakNo:{}", request.getId(), request.getEvrakDetay().getEvrakNo(), ex);
            throw new RuntimeException(ex);
        }
    }

}

