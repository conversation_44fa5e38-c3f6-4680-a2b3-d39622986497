/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import iym.makos.api.client.gen.model.MahkemeKararDetay;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Mahkeme karar bilgileri
 */
@JsonPropertyOrder({
  MahkemeKararBilgisi.JSON_PROPERTY_MAHKEME_KARAR_TIPI,
  MahkemeKararBilgisi.JSON_PROPERTY_MAHKEME_KARAR_DETAY
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class MahkemeKararBilgisi {
  /**
   * Gets or Sets mahkemeKararTipi
   */
  public enum MahkemeKararTipiEnum {
    _100("100"),
    
    _150("150"),
    
    _151("151"),
    
    _200("200"),
    
    _300("300"),
    
    _350("350"),
    
    _400("400"),
    
    _410("410"),
    
    _450("450"),
    
    _510("510"),
    
    _511("511"),
    
    _520("520"),
    
    _521("521"),
    
    _530("530"),
    
    _600("600"),
    
    _700("700"),
    
    _710("710"),
    
    _720("720"),
    
    _730("730"),
    
    _800("800"),
    
    _900("900"),
    
    _910("910"),
    
    _599("599"),
    
    _920("920"),
    
    _6002("600");

    private String value;

    MahkemeKararTipiEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static MahkemeKararTipiEnum fromValue(String value) {
      for (MahkemeKararTipiEnum b : MahkemeKararTipiEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_MAHKEME_KARAR_TIPI = "mahkemeKararTipi";
  private MahkemeKararTipiEnum mahkemeKararTipi;

  public static final String JSON_PROPERTY_MAHKEME_KARAR_DETAY = "mahkemeKararDetay";
  private MahkemeKararDetay mahkemeKararDetay;

  public MahkemeKararBilgisi() {
  }

  public MahkemeKararBilgisi mahkemeKararTipi(MahkemeKararTipiEnum mahkemeKararTipi) {
    
    this.mahkemeKararTipi = mahkemeKararTipi;
    return this;
  }

   /**
   * Get mahkemeKararTipi
   * @return mahkemeKararTipi
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_TIPI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public MahkemeKararTipiEnum getMahkemeKararTipi() {
    return mahkemeKararTipi;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_TIPI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMahkemeKararTipi(MahkemeKararTipiEnum mahkemeKararTipi) {
    this.mahkemeKararTipi = mahkemeKararTipi;
  }


  public MahkemeKararBilgisi mahkemeKararDetay(MahkemeKararDetay mahkemeKararDetay) {
    
    this.mahkemeKararDetay = mahkemeKararDetay;
    return this;
  }

   /**
   * Get mahkemeKararDetay
   * @return mahkemeKararDetay
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DETAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public MahkemeKararDetay getMahkemeKararDetay() {
    return mahkemeKararDetay;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DETAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMahkemeKararDetay(MahkemeKararDetay mahkemeKararDetay) {
    this.mahkemeKararDetay = mahkemeKararDetay;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MahkemeKararBilgisi mahkemeKararBilgisi = (MahkemeKararBilgisi) o;
    return Objects.equals(this.mahkemeKararTipi, mahkemeKararBilgisi.mahkemeKararTipi) &&
        Objects.equals(this.mahkemeKararDetay, mahkemeKararBilgisi.mahkemeKararDetay);
  }

  @Override
  public int hashCode() {
    return Objects.hash(mahkemeKararTipi, mahkemeKararDetay);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MahkemeKararBilgisi {\n");
    sb.append("    mahkemeKararTipi: ").append(toIndentedString(mahkemeKararTipi)).append("\n");
    sb.append("    mahkemeKararDetay: ").append(toIndentedString(mahkemeKararDetay)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

