package iym.db.jpa.dao;

import iym.common.model.entity.iym.MahkemeKararSuclar;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for MahkemeKararSucRepo entity
 */
@Repository
public interface MahkemeKararSucTipiRepo extends JpaRepository<MahkemeKararSuclar, Long> {

    List<MahkemeKararSuclar> findByMahkemeKararId(Long mahkemeKararId);

    Optional<MahkemeKararSuclar> findByMahkemeKararIdAndSucTipKodu(Long mahkemeKararId, String sucTipKodu);
}
