# Test Environment Configuration for Backend
# Multi-Database Configuration for Testing

# Oracle Database configuration (Primary) - H2 in-memory for testing
spring.datasource.oracle.jdbc-url=jdbc:h2:mem:oracle_testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.oracle.driver-class-name=org.h2.Driver
spring.datasource.oracle.username=sa
spring.datasource.oracle.password=

# PostgreSQL Database configuration (Secondary) - H2 in-memory for testing
spring.datasource.postgresql.jdbc-url=jdbc:h2:mem:postgresql_testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.postgresql.driver-class-name=org.h2.Driver
spring.datasource.postgresql.username=sa
spring.datasource.postgresql.password=

# Oracle JPA configuration for testing (H2 in-memory)
spring.jpa.oracle.hibernate.ddl-auto=create-drop
spring.jpa.oracle.show-sql=true
spring.jpa.oracle.hibernate.format_sql=true
spring.jpa.oracle.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.oracle.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect

# PostgreSQL JPA configuration for testing (H2 in-memory)
spring.jpa.postgresql.hibernate.ddl-auto=create-drop
spring.jpa.postgresql.show-sql=true
spring.jpa.postgresql.hibernate.format_sql=true
spring.jpa.postgresql.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.postgresql.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect
spring.jpa.postgresql.properties.hibernate.hbm2ddl.auto=create-drop
spring.jpa.postgresql.properties.hibernate.temp.use_jdbc_metadata_defaults=false

# Logging configuration for tests
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
logging.level.iym.backend.postgresql=DEBUG

# JWT Configuration for tests
app.jwtSecret=test-secret-key-asdRET567-asdHd-52sdauer-dfgZSDfas5sCd34df-123dFTH56HG-asd45FgbsdDd334-aasd456fdvb
app.jwtExpirationInSec=1800

# CORS Configuration for tests
cors.allowed.origins=http://localhost:3000,http://localhost:4000

# Makos API configuration for tests
makos.api.base-url=http://localhost:5000/makosapi
makos.api.username=test_user
makos.api.password=test_password
makos.api.connect-timeout=5000
makos.api.read-timeout=30000
