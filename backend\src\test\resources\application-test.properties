# Test Environment Configuration for Backend
# Database Configuration for Testing

# H2 in-memory database for testing
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# JPA configuration for testing (H2 in-memory)
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect
spring.jpa.properties.hibernate.hbm2ddl.auto=create-drop
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false

# Logging configuration for tests
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
logging.level.iym.backend=DEBUG

# JWT Configuration for tests
app.jwtSecret=test-secret-key-asdRET567-asdHd-52sdauer-dfgZSDfas5sCd34df-123dFTH56HG-asd45FgbsdDd334-aasd456fdvb
app.jwtExpirationInSec=1800

# CORS Configuration for tests
cors.allowed.origins=http://localhost:3000,http://localhost:4000

# Makos API configuration for tests
makos.api.base-url=http://localhost:5000/makosapi
makos.api.username=test_user
makos.api.password=test_password
makos.api.connect-timeout=5000
makos.api.read-timeout=30000
