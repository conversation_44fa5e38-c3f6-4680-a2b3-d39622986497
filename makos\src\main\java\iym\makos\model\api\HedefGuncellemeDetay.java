package iym.makos.model.api;

import io.swagger.v3.oas.annotations.media.Schema;
import iym.common.model.api.Hedef;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

import java.util.List;

@Data
@Builder
@Jacksonized
@ToString
@EqualsAndHashCode
public class HedefGuncellemeDetay {

    @NotNull
    @Schema(description = "Değişiklik yapılacak hedefin hedefNo ve hedefTip bilgileri")
    private Hedef hedef;

    @NotNull
    @Size(min = 1)
    @Valid
    private List<HedefGuncellemeBilgi> hedefGuncellemeBilgiListesi;

}
