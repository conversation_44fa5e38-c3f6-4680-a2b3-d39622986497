package iym.backend.config;

import iym.backend.config.properties.PostgresqlJpaProperties;
import jakarta.persistence.EntityManagerFactory;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * PostgreSQL DataSource configuration for secondary database
 */
@Configuration
@ConditionalOnProperty(
    name = "app.datasource.postgresql.enabled",
    havingValue = "true",
    matchIfMissing = true
)
@EnableJpaRepositories(
        basePackages = "iym.backend.postgresql",
        entityManagerFactoryRef = "postgresqlEntityManagerFactory",
        transactionManagerRef = "postgresqlTransactionManager"
)
@Slf4j
public class PostgreSQLDataSourceConfig {

    private final PostgresqlJpaProperties postgresqlJpaProperties;

    /**
     * Constructor injection for better dependency management
     * @param postgresqlJpaProperties PostgreSQL JPA configuration properties
     */
    public PostgreSQLDataSourceConfig(@Qualifier("postgresqlJpaProperties") PostgresqlJpaProperties postgresqlJpaProperties) {
        this.postgresqlJpaProperties = postgresqlJpaProperties;
    }

    @Bean(name = "postgresqlDataSource")
    @ConfigurationProperties("spring.datasource.postgresql")
    public DataSource postgresqlDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "postgresqlEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean postgresqlEntityManagerFactory(
            DataSource postgresqlDataSource) {

        LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
        em.setDataSource(postgresqlDataSource);
        em.setPackagesToScan("iym.backend.postgresql");

        // Get JPA properties with PostgreSQL defaults applied by PostgresqlJpaProperties
        Map<String, String> jpaProperties = new HashMap<>(postgresqlJpaProperties.getProperties());

        // Determine the correct dialect based on properties (test vs production)
        String dialect = jpaProperties.getOrDefault("hibernate.dialect", "org.hibernate.dialect.PostgreSQLDialect");

        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        vendorAdapter.setDatabasePlatform(dialect);
        em.setJpaVendorAdapter(vendorAdapter);

        log.info("PostgreSQL EntityManagerFactory - Using dialect: {}", dialect);
        log.info("PostgreSQL EntityManagerFactory - Final JPA Properties:");
        jpaProperties.forEach((key, value) -> {
            if (key.contains("naming") || key.contains("dialect") || key.contains("generator") || key.contains("ddl")) {
                log.info("  {} = {}", key, value);
            }
        });

        em.setJpaPropertyMap(jpaProperties);

        return em;
    }

    @Bean(name = "postgresqlTransactionManager")
    public PlatformTransactionManager postgresqlTransactionManager(
            EntityManagerFactory postgresqlEntityManagerFactory) {
        return new JpaTransactionManager(postgresqlEntityManagerFactory);
    }
}
