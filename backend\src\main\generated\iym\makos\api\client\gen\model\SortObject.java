/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * SortObject
 */
@JsonPropertyOrder({
  SortObject.JSON_PROPERTY_EMPTY,
  SortObject.JSON_PROPERTY_UNSORTED,
  SortObject.JSON_PROPERTY_SORTED
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class SortObject {
  public static final String JSON_PROPERTY_EMPTY = "empty";
  private Boolean empty;

  public static final String JSON_PROPERTY_UNSORTED = "unsorted";
  private Boolean unsorted;

  public static final String JSON_PROPERTY_SORTED = "sorted";
  private Boolean sorted;

  public SortObject() {
  }

  public SortObject empty(Boolean empty) {
    
    this.empty = empty;
    return this;
  }

   /**
   * Get empty
   * @return empty
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EMPTY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getEmpty() {
    return empty;
  }


  @JsonProperty(JSON_PROPERTY_EMPTY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEmpty(Boolean empty) {
    this.empty = empty;
  }


  public SortObject unsorted(Boolean unsorted) {
    
    this.unsorted = unsorted;
    return this;
  }

   /**
   * Get unsorted
   * @return unsorted
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_UNSORTED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getUnsorted() {
    return unsorted;
  }


  @JsonProperty(JSON_PROPERTY_UNSORTED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUnsorted(Boolean unsorted) {
    this.unsorted = unsorted;
  }


  public SortObject sorted(Boolean sorted) {
    
    this.sorted = sorted;
    return this;
  }

   /**
   * Get sorted
   * @return sorted
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SORTED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getSorted() {
    return sorted;
  }


  @JsonProperty(JSON_PROPERTY_SORTED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSorted(Boolean sorted) {
    this.sorted = sorted;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SortObject sortObject = (SortObject) o;
    return Objects.equals(this.empty, sortObject.empty) &&
        Objects.equals(this.unsorted, sortObject.unsorted) &&
        Objects.equals(this.sorted, sortObject.sorted);
  }

  @Override
  public int hashCode() {
    return Objects.hash(empty, unsorted, sorted);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SortObject {\n");
    sb.append("    empty: ").append(toIndentedString(empty)).append("\n");
    sb.append("    unsorted: ").append(toIndentedString(unsorted)).append("\n");
    sb.append("    sorted: ").append(toIndentedString(sorted)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

