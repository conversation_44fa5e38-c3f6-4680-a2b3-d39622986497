/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import iym.makos.api.client.gen.model.IDSucTipiGuncellemeRequest;
import java.io.File;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * SucTipiGuncelleRequest
 */
@JsonPropertyOrder({
  SucTipiGuncelleRequest.JSON_PROPERTY_MAHKEME_KARAR_DOSYASI,
  SucTipiGuncelleRequest.JSON_PROPERTY_MAHKEME_KARAR_DETAY
})
@JsonTypeName("sucTipiGuncelle_request")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class SucTipiGuncelleRequest {
  public static final String JSON_PROPERTY_MAHKEME_KARAR_DOSYASI = "mahkemeKararDosyasi";
  private File mahkemeKararDosyasi;

  public static final String JSON_PROPERTY_MAHKEME_KARAR_DETAY = "mahkemeKararDetay";
  private IDSucTipiGuncellemeRequest mahkemeKararDetay;

  public SucTipiGuncelleRequest() {
  }

  public SucTipiGuncelleRequest mahkemeKararDosyasi(File mahkemeKararDosyasi) {
    
    this.mahkemeKararDosyasi = mahkemeKararDosyasi;
    return this;
  }

   /**
   * Get mahkemeKararDosyasi
   * @return mahkemeKararDosyasi
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DOSYASI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public File getMahkemeKararDosyasi() {
    return mahkemeKararDosyasi;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DOSYASI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMahkemeKararDosyasi(File mahkemeKararDosyasi) {
    this.mahkemeKararDosyasi = mahkemeKararDosyasi;
  }


  public SucTipiGuncelleRequest mahkemeKararDetay(IDSucTipiGuncellemeRequest mahkemeKararDetay) {
    
    this.mahkemeKararDetay = mahkemeKararDetay;
    return this;
  }

   /**
   * Get mahkemeKararDetay
   * @return mahkemeKararDetay
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DETAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public IDSucTipiGuncellemeRequest getMahkemeKararDetay() {
    return mahkemeKararDetay;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DETAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMahkemeKararDetay(IDSucTipiGuncellemeRequest mahkemeKararDetay) {
    this.mahkemeKararDetay = mahkemeKararDetay;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SucTipiGuncelleRequest sucTipiGuncelleRequest = (SucTipiGuncelleRequest) o;
    return Objects.equals(this.mahkemeKararDosyasi, sucTipiGuncelleRequest.mahkemeKararDosyasi) &&
        Objects.equals(this.mahkemeKararDetay, sucTipiGuncelleRequest.mahkemeKararDetay);
  }

  @Override
  public int hashCode() {
    return Objects.hash(mahkemeKararDosyasi, mahkemeKararDetay);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SucTipiGuncelleRequest {\n");
    sb.append("    mahkemeKararDosyasi: ").append(toIndentedString(mahkemeKararDosyasi)).append("\n");
    sb.append("    mahkemeKararDetay: ").append(toIndentedString(mahkemeKararDetay)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

