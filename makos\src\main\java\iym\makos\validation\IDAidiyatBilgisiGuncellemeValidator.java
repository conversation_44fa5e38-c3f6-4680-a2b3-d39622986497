package iym.makos.validation;

import iym.common.model.api.Evrak<PERSON>;
import iym.common.model.api.GuncellemeTip;
import iym.common.model.api.KararTuru;
import iym.common.model.api.MahkemeKararTip;
import iym.common.model.entity.iym.MahkemeAidiyat;
import iym.common.model.entity.iym.MahkemeKarar;
import iym.common.service.db.DbMahkemeAidiyatService;
import iym.common.service.db.DbMahkemeKararService;
import iym.common.util.CommonUtils;
import iym.common.validation.ValidationResult;
import iym.makos.dto.id.IDAidiyatBilgisiGuncellemeRequest;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.model.api.AidiyatGuncellemeDetay;
import iym.makos.model.api.AidiyatGuncellemeKararDetay;
import iym.makos.model.api.MahkemeKararDetay;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Slf4j
public class IDAidiyatBilgisiGuncellemeValidator extends MahkemeKararRequestValidatorBase<IDAidiyatBilgisiGuncellemeRequest> {

    private final DbMahkemeKararService dbMahkemeKararService;
    private final DbMahkemeAidiyatService dbMahkemeAidiyatService;

    @Autowired
    public IDAidiyatBilgisiGuncellemeValidator(DbMahkemeKararService dbMahkemeKararService,
                                               DbMahkemeAidiyatService dbMahkemeAidiyatService
    ) {
        this.dbMahkemeKararService = dbMahkemeKararService;
        this.dbMahkemeAidiyatService = dbMahkemeAidiyatService;

    }

    @Override
    public ValidationResult validate(IDAidiyatBilgisiGuncellemeRequest request) {

        try {
            ValidationResult validationResult = super.validate(request);
            if (!validationResult.isValid()) {
                return validationResult;
            }
            MahkemeKararTip mahkemeKararTip = request.getMahkemeKararBilgisi().getMahkemeKararTipi();
            String evrakGelenKurumKodu = request.getEvrakDetay().getEvrakKurumKodu();
            EvrakKurum evrakKurum = CommonUtils.getEvrakKurum(evrakGelenKurumKodu);

            for (AidiyatGuncellemeKararDetay aidiyatGuncellemeKararDetay : CommonUtils.safeList(request.getAidiyatGuncellemeKararDetayListesi())) {

                MahkemeKararDetay iliskiliMahkemeKararDetay = aidiyatGuncellemeKararDetay.getMahkemeKararDetay();
                if (iliskiliMahkemeKararDetay == null) {
                    validationResult.addFailedReason("İlişkili mahkeme karar boş olamaz.");
                } else {
                    Optional<MahkemeKarar> iliskiliMahkemeKararOpt = dbMahkemeKararService.findBy(iliskiliMahkemeKararDetay.getMahkemeIlIlceKodu()
                            , iliskiliMahkemeKararDetay.getMahkemeKodu()
                            , iliskiliMahkemeKararDetay.getMahkemeKararNo()
                            , iliskiliMahkemeKararDetay.getSorusturmaNo());

                    if (iliskiliMahkemeKararOpt.isEmpty()) {
                        String errorStr = String.format(MakosResponseErrorCodes.MK_BULUNAMADI, iliskiliMahkemeKararDetay.getMahkemeIlIlceKodu()
                                , iliskiliMahkemeKararDetay.getMahkemeKodu(), iliskiliMahkemeKararDetay.getMahkemeKararNo()
                                , iliskiliMahkemeKararDetay.getSorusturmaNo());
                        validationResult.addFailedReason(errorStr);
                    } else {

                        for (AidiyatGuncellemeDetay aidiyatGuncellemeDetay : CommonUtils.safeList(aidiyatGuncellemeKararDetay.getAidiyatGuncellemeDetayListesi())) {
                            String aidiyatKodu = aidiyatGuncellemeDetay.getAidiyatKodu();

                            String aidiyatUyari = CommonUtils.aidiyatEklemeUyarisi(aidiyatKodu, mahkemeKararTip, evrakKurum);
                            if(!CommonUtils.isNullOrEmpty(aidiyatUyari)){
                                validationResult.addFailedReason(aidiyatUyari);
                            }

                            Optional<MahkemeAidiyat> aidiyatOpt = dbMahkemeAidiyatService.findByMahkemeKararIdAndAidiyatKod(iliskiliMahkemeKararOpt.get().getId(), aidiyatKodu);
                            if (aidiyatGuncellemeDetay.getGuncellemeTip() == GuncellemeTip.EKLE) {
                                if (aidiyatOpt.isPresent()) {
                                    validationResult.addFailedReason(aidiyatKodu + " aidiyatı mahkeme karar bilgisinde bulunduğu için eklenemez. ");
                                }
                            } else if (aidiyatGuncellemeDetay.getGuncellemeTip() == GuncellemeTip.CIKAR) {
                                if (aidiyatOpt.isEmpty()) {
                                    validationResult.addFailedReason(aidiyatKodu + " aidiyatı mahkeme karar bilgisinde bulunamadığı için çıkarılamaz.");
                                }
                            }
                        }
                    }
                }
            }

            return validationResult;
        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }

    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME;
    }
}

