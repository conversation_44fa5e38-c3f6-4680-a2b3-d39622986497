package iym.backend.config.properties;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;

import java.util.Map;

/**
 * Oracle JPA Properties configuration that extends Spring Boot's default JpaProperties
 * Maps properties from application.properties with prefix 'spring.jpa.oracle'
 *
 * This approach preserves all Spring Boot defaults while allowing Oracle-specific customizations
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OracleJpaProperties extends JpaProperties {

    /**
     * Default schema for Oracle
     */
    private String defaultSchema = "iym";

    /**
     * Get all JPA properties as a Map for EntityManagerFactory
     * Inherits all Spring Boot defaults and adds Oracle-specific properties
     */
    @Override
    public Map<String, String> getProperties() {
        Map<String, String> properties = super.getProperties();

        // Add Oracle-specific properties
        if (defaultSchema != null) {
            properties.put("hibernate.default_schema", defaultSchema);
        }

        return properties;
    }
}
