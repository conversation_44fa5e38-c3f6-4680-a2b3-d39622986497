openapi: 3.0.1
info:
  title: MAKOS OpenAPI definition
  description: MAKOS Application
  version: v1.0
servers:
- url: http://localhost:5000/makosapi
  description: Generated server url
security:
- BasicAuth: []
paths:
  /user/update:
    post:
      tags:
      - user-controller
      operationId: updateUser
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/UpdateUserResponse'
  /user/delete:
    post:
      tags:
      - user-controller
      operationId: deleteUser
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteUserRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/DeleteUserResponse'
  /user/deactivate:
    post:
      tags:
      - user-controller
      operationId: deactivateUser
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UsernameRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/DeactivateUserResponse'
  /user/add:
    post:
      tags:
      - user-controller
      operationId: addUser
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddUserRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AddUserResponse'
  /user/activate:
    post:
      tags:
      - user-controller
      operationId: activateUser
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UsernameRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ActivateUserResponse'
  /mahkemeKarar/yeniKararID:
    post:
      tags:
      - mahkeme-karar-controller
      operationId: yeniKararID
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
              - jsonData
              - mahkemeKararDosyasiID
              type: object
              properties:
                mahkemeKararDosyasiID:
                  type: string
                  format: binary
                jsonData:
                  $ref: '#/components/schemas/IDYeniKararRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDYeniKararResponse'
  /mahkemeKarar/uzatmaKarariID:
    post:
      tags:
      - mahkeme-karar-controller
      operationId: uzatmaKarariID
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
              - mahkemeKararDetayID
              - mahkemeKararDosyasiID
              type: object
              properties:
                mahkemeKararDosyasiID:
                  type: string
                  format: binary
                mahkemeKararDetayID:
                  $ref: '#/components/schemas/IDUzatmaKarariRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDUzatmaKarariResponse'
  /mahkemeKarar/sucTipiGuncelle:
    post:
      tags:
      - mahkeme-karar-controller
      operationId: sucTipiGuncelle
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
              - mahkemeKararDetay
              - mahkemeKararDosyasi
              type: object
              properties:
                mahkemeKararDosyasi:
                  type: string
                  format: binary
                mahkemeKararDetay:
                  $ref: '#/components/schemas/IDSucTipiGuncellemeRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDSucTipiGuncellemeResponse'
  /mahkemeKarar/sonlandirmaKarariID:
    post:
      tags:
      - mahkeme-karar-controller
      operationId: sonlandirmaKarariID
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
              - mahkemeKararDetayID
              - mahkemeKararDosyasiID
              type: object
              properties:
                mahkemeKararDosyasiID:
                  type: string
                  format: binary
                mahkemeKararDetayID:
                  $ref: '#/components/schemas/IDSonlandirmaKarariRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDSonlandirmaKarariResponse'
  /mahkemeKarar/mahkemeBilgiGuncelle:
    post:
      tags:
      - mahkeme-karar-controller
      operationId: mahkemeBilgiGuncelle
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
              - mahkemeKararDetay
              - mahkemeKararDosyasi
              type: object
              properties:
                mahkemeKararDosyasi:
                  type: string
                  format: binary
                mahkemeKararDetay:
                  $ref: '#/components/schemas/IDMahkemeKararGuncellemeRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDMahkemeKararGuncellemeResponse'
  /mahkemeKarar/kararGonderIT:
    post:
      tags:
      - mahkeme-karar-controller
      operationId: kararGonderIT
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
              - mahkemeKararDetayIT
              - mahkemeKararDosyasiIT
              type: object
              properties:
                mahkemeKararDosyasiIT:
                  type: string
                  format: binary
                mahkemeKararDetayIT:
                  $ref: '#/components/schemas/ITKararRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ITKararResponse'
  /mahkemeKarar/hedefBilgiGuncelle:
    post:
      tags:
      - mahkeme-karar-controller
      operationId: hedefBilgiGuncelle
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
              - mahkemeKararDetay
              - mahkemeKararDosyasi
              type: object
              properties:
                mahkemeKararDosyasi:
                  type: string
                  format: binary
                mahkemeKararDetay:
                  $ref: '#/components/schemas/IDHedefGuncellemeRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDHedefGuncellemeResponse'
  /mahkemeKarar/aidiyatBilgisiGuncelle:
    post:
      tags:
      - mahkeme-karar-controller
      operationId: aidiyatBilgisiGuncelle
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
              - mahkemeKararDetay
              - mahkemeKararDosyasi
              type: object
              properties:
                mahkemeKararDosyasi:
                  type: string
                  format: binary
                mahkemeKararDetay:
                  $ref: '#/components/schemas/IDAidiyatBilgisiGuncellemeRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDAidiyatBilgisiGuncellemeResponse'
  /auth/register:
    post:
      tags:
      - auth-controller
      operationId: register
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/RegisterResponse'
  /auth/login:
    post:
      tags:
      - auth-controller
      operationId: login
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/LoginResponse'
  /auth/changePassword:
    post:
      tags:
      - auth-controller
      operationId: changePassword
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChangePasswordRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ChangePasswordResponse'
  /user:
    get:
      tags:
      - user-controller
      operationId: getUser
      parameters:
      - name: username
        in: query
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/GetUserResponse'
  /user/{id}:
    get:
      tags:
      - user-controller
      operationId: getUserById
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/GetUserByIdResponse'
  /user/getUsersForAdmin:
    get:
      tags:
      - user-controller
      operationId: getUsersForAdmin
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/UsersListResponse'
  /user/getAllUsers:
    get:
      tags:
      - user-controller
      operationId: getAllUsers
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/UsersListResponse'
  /check/healthCheck:
    get:
      tags:
      - health-check-controller
      operationId: healthCheck
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/HealthCheckResponse'
  /check/healthCheckQueryAdmin:
    get:
      tags:
      - health-check-controller
      operationId: healthCheckQueryAdmin
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/HealthCheckResponse'
  /check/healthCheckAuthorized:
    get:
      tags:
      - health-check-controller
      operationId: healthCheckAuthorized
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/HealthCheckResponse'
  /check/healthCheckAdmin:
    get:
      tags:
      - health-check-controller
      operationId: healthCheckAdmin
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/HealthCheckResponse'
  /audit/getMakosUserAuditLogsByUsername:
    get:
      tags:
      - audit-controller
      operationId: getMakosUserAuditLogsByUsername
      parameters:
      - name: username
        in: query
        required: true
        schema:
          type: string
      - name: page
        in: query
        required: false
        schema:
          type: integer
          format: int32
          default: 0
      - name: count
        in: query
        required: false
        schema:
          type: integer
          format: int32
          default: 200
      - name: sortBy
        in: query
        required: false
        schema:
          type: string
          default: requestTime
      - name: sortDirection
        in: query
        required: false
        schema:
          type: string
          default: desc
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/GetMakosUserAuditLogsByUsernameResponse'
  /audit/getMakosUserAuditLogsByType:
    get:
      tags:
      - audit-controller
      operationId: getMakosUserAuditLogsByType
      parameters:
      - name: auditType
        in: query
        required: true
        schema:
          type: string
      - name: page
        in: query
        required: false
        schema:
          type: integer
          format: int32
          default: 0
      - name: count
        in: query
        required: false
        schema:
          type: integer
          format: int32
          default: 200
      - name: sortBy
        in: query
        required: false
        schema:
          type: string
          default: requestTime
      - name: sortDirection
        in: query
        required: false
        schema:
          type: string
          default: desc
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/GetMakosUserAuditLogsByTypeResponse'
  /audit/getMakosUserAuditLogList:
    get:
      tags:
      - audit-controller
      operationId: getMakosUserAuditLogList
      parameters:
      - name: page
        in: query
        required: false
        schema:
          type: integer
          format: int32
          default: 0
      - name: count
        in: query
        required: false
        schema:
          type: integer
          format: int32
          default: 200
      - name: sortBy
        in: query
        required: false
        schema:
          type: string
          default: requestTime
      - name: sortDirection
        in: query
        required: false
        schema:
          type: string
          default: desc
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/GetMakosUserAuditLogListResponse'
  /audit/getMakosUserAuditLog/{id}:
    get:
      tags:
      - audit-controller
      operationId: getMakosUserAuditLog
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/GetMakosUserAuditLogResponse'
components:
  schemas:
    MakosUser:
      required:
      - role
      - status
      - username
      type: object
      properties:
        id:
          type: integer
          format: int64
        username:
          maxLength: 100
          minLength: 4
          type: string
        status:
          type: string
          enum:
          - PASSIVE
          - ACTIVE
          - LOCKED
        role:
          type: string
          enum:
          - ROLE_ADMIN
          - ROLE_QUERY_ADMIN
          - ROLE_KURUM_TEMSILCISI
          - ROLE_KURUM_KULLANICI
        kurum:
          type: string
          enum:
          - ADLI
          - EMNIYET
          - MIT
          - JANDARMA
          - BTK
          - EMNIYET_SIBER
        newPassword:
          type: string
    UpdateUserRequest:
      required:
      - id
      - user
      type: object
      properties:
        id:
          type: integer
          format: int64
        user:
          $ref: '#/components/schemas/MakosUser'
    ApiResponse:
      required:
      - responseCode
      type: object
      properties:
        responseCode:
          type: string
          enum:
          - SUCCESS
          - FAILED
        responseMessage:
          type: string
    UpdateUserResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
        id:
          type: integer
          format: int64
    DeleteUserRequest:
      required:
      - userId
      type: object
      properties:
        userId:
          type: integer
          format: int64
    DeleteUserResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
    UsernameRequest:
      required:
      - username
      type: object
      properties:
        username:
          type: string
    DeactivateUserResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
    AddUserRequest:
      required:
      - id
      - user
      type: object
      properties:
        id:
          type: integer
          format: int64
        user:
          $ref: '#/components/schemas/MakosUser'
    AddUserResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
        id:
          type: integer
          format: int64
    ActivateUserResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
    EvrakDetay:
      required:
      - evrakKurumKodu
      - evrakNo
      - evrakTarihi
      - evrakTuru
      - geldigiIlIlceKodu
      type: object
      properties:
        evrakNo:
          maxLength: 50
          minLength: 0
          type: string
        evrakTarihi:
          type: string
          format: date-time
        evrakKurumKodu:
          type: string
        evrakTuru:
          type: string
          enum:
          - "0"
          - "1"
          - "2"
        havaleBirimi:
          type: string
        aciklama:
          type: string
        geldigiIlIlceKodu:
          type: string
        acilmi:
          type: boolean
        evrakKonusu:
          type: string
    Hedef:
      required:
      - hedefNo
      - hedefTip
      type: object
      properties:
        hedefNo:
          type: string
        hedefTip:
          type: string
          enum:
          - "10"
          - "20"
          - "30"
          - "40"
          - "41"
          - "42"
          - "43"
          - "44"
          - "50"
          - "51"
          - "52"
          - "53"
          - "54"
          - "55"
          - "56"
          - "60"
          - "70"
          - "71"
          - "80"
          - "81"
          - "82"
          - "83"
          - "90"
          - "91"
          - "92"
          - "99"
          - "200"
          - "210"
          - "201"
          - "211"
      description: Değişiklik yapılacak hedefin hedefNo ve hedefTip bilgileri
    HedefDetayID:
      required:
      - baslamaTarihi
      - hedefNoAdSoyad
      - sure
      - sureTip
      type: object
      properties:
        hedefNoAdSoyad:
          $ref: '#/components/schemas/HedefWithAdSoyad'
        baslamaTarihi:
          type: string
          format: date-time
        sureTip:
          type: string
          enum:
          - "1"
          - "2"
          - "0"
        sure:
          type: integer
          format: int32
        ilgiliMahkemeKararDetayi:
          $ref: '#/components/schemas/MahkemeKararDetay'
        uzatmaSayisi:
          type: integer
          description: Uzatma Sayisi. Sadece uzatma kararlarinda gerekli
          format: int32
        hedefAidiyatKodlari:
          type: array
          items:
            type: string
        canakNo:
          type: string
          description: Canak numarası. Sadece yeni kararda girilebilir. Zorunlu olmayan
            alan
    HedefWithAdSoyad:
      required:
      - hedef
      - hedefAd
      - hedefSoyad
      type: object
      properties:
        hedef:
          $ref: '#/components/schemas/Hedef'
        hedefAd:
          type: string
        hedefSoyad:
          type: string
        tcKimlikNo:
          type: string
    IDYeniKararRequest:
      required:
      - evrakDetay
      - hedefDetayListesi
      - id
      - kararTuru
      - mahkemeKararBilgisi
      type: object
      properties:
        id:
          type: string
          format: uuid
        kararTuru:
          type: string
          enum:
          - "0"
          - "1"
          - "2"
          - "3"
          - "4"
          - "5"
          - "6"
          - "7"
          - "8"
          - "9"
        evrakDetay:
          $ref: '#/components/schemas/EvrakDetay'
        mahkemeKararBilgisi:
          $ref: '#/components/schemas/MahkemeKararBilgisi'
        hedefDetayListesi:
          maxItems: 2147483647
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/HedefDetayID'
        mahkemeAidiyatKodlari:
          type: array
          items:
            type: string
        mahkemeSucTipiKodlari:
          type: array
          items:
            type: string
      description: ID Mahkeme Karar Detaylari
    MahkemeKararBilgisi:
      required:
      - mahkemeKararDetay
      - mahkemeKararTipi
      type: object
      properties:
        mahkemeKararTipi:
          type: string
          enum:
          - "100"
          - "150"
          - "151"
          - "200"
          - "300"
          - "350"
          - "400"
          - "410"
          - "450"
          - "510"
          - "511"
          - "520"
          - "521"
          - "530"
          - "600"
          - "700"
          - "710"
          - "720"
          - "730"
          - "800"
          - "900"
          - "910"
          - "599"
          - "920"
          - "600"
        mahkemeKararDetay:
          $ref: '#/components/schemas/MahkemeKararDetay'
      description: Mahkeme karar bilgileri
    MahkemeKararDetay:
      required:
      - mahkemeIlIlceKodu
      - mahkemeKodu
      type: object
      properties:
        mahkemeKodu:
          type: string
        mahkemeIlIlceKodu:
          type: string
        mahkemeKararNo:
          type: string
        sorusturmaNo:
          type: string
        aciklama:
          type: string
      description: Aidiyat değişikliği yapılacak mahkeme karar bilgileri
    IDYeniKararResponse:
      required:
      - btkEvrakId
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        btkEvrakId:
          type: integer
          format: int64
    MakosApiResponse:
      required:
      - responseCode
      type: object
      properties:
        responseCode:
          type: string
          enum:
          - SUCCESS
          - INVALID_REQUEST
          - FAILED
        responseMessage:
          type: string
    IDUzatmaKarariRequest:
      required:
      - evrakDetay
      - hedefDetayListesi
      - id
      - kararTuru
      - mahkemeKararBilgisi
      type: object
      properties:
        id:
          type: string
          format: uuid
        kararTuru:
          type: string
          enum:
          - "0"
          - "1"
          - "2"
          - "3"
          - "4"
          - "5"
          - "6"
          - "7"
          - "8"
          - "9"
        evrakDetay:
          $ref: '#/components/schemas/EvrakDetay'
        mahkemeKararBilgisi:
          $ref: '#/components/schemas/MahkemeKararBilgisi'
        hedefDetayListesi:
          maxItems: 2147483647
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/HedefDetayID'
        mahkemeAidiyatKodlari:
          type: array
          items:
            type: string
        mahkemeSucTipiKodlari:
          type: array
          items:
            type: string
      description: ID Mahkeme Karar Detaylari
    IDUzatmaKarariResponse:
      required:
      - evrakId
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        evrakId:
          type: integer
          format: int64
    IDSucTipiGuncellemeRequest:
      required:
      - evrakDetay
      - id
      - kararTuru
      - mahkemeKararBilgisi
      - sucTipiGuncellemeKararDetayListesi
      type: object
      properties:
        id:
          type: string
          format: uuid
        kararTuru:
          type: string
          enum:
          - "0"
          - "1"
          - "2"
          - "3"
          - "4"
          - "5"
          - "6"
          - "7"
          - "8"
          - "9"
        evrakDetay:
          $ref: '#/components/schemas/EvrakDetay'
        mahkemeKararBilgisi:
          $ref: '#/components/schemas/MahkemeKararBilgisi'
        sucTipiGuncellemeKararDetayListesi:
          maxItems: 2147483647
          minItems: 1
          type: array
          description: Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek
            aidiyat bilgileri
          items:
            $ref: '#/components/schemas/SucTipiGuncellemeKararDetay'
      description: Mahkeme Karar Detaylari
    SucTipiGuncellemeDetay:
      required:
      - guncellemeTip
      - sucTipiKodu
      type: object
      properties:
        guncellemeTip:
          type: string
          enum:
          - "0"
          - "1"
        sucTipiKodu:
          type: string
    SucTipiGuncellemeKararDetay:
      required:
      - mahkemeKararDetay
      - sucTipiGuncellemeDetayListesi
      type: object
      properties:
        mahkemeKararDetay:
          $ref: '#/components/schemas/MahkemeKararDetay'
        sucTipiGuncellemeDetayListesi:
          maxItems: 2147483647
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/SucTipiGuncellemeDetay'
      description: Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek
        aidiyat bilgileri
    IDSucTipiGuncellemeResponse:
      required:
      - evrakId
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        evrakId:
          type: integer
          format: int64
    IDSonlandirmaKarariRequest:
      required:
      - evrakDetay
      - hedefDetayListesi
      - id
      - kararTuru
      - mahkemeKararBilgisi
      type: object
      properties:
        id:
          type: string
          format: uuid
        kararTuru:
          type: string
          enum:
          - "0"
          - "1"
          - "2"
          - "3"
          - "4"
          - "5"
          - "6"
          - "7"
          - "8"
          - "9"
        evrakDetay:
          $ref: '#/components/schemas/EvrakDetay'
        mahkemeKararBilgisi:
          $ref: '#/components/schemas/MahkemeKararBilgisi'
        hedefDetayListesi:
          maxItems: 2147483647
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/HedefDetayID'
        mahkemeAidiyatKodlari:
          type: array
          items:
            type: string
        mahkemeSucTipiKodlari:
          type: array
          items:
            type: string
      description: ID Mahkeme Karar Detaylari
    IDSonlandirmaKarariResponse:
      required:
      - evrakId
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        evrakId:
          type: integer
          format: int64
    IDMahkemeKararGuncellemeRequest:
      required:
      - evrakDetay
      - id
      - kararTuru
      - mahkemeKararBilgisi
      - mahkemeKararGuncellemeDetayListesi
      type: object
      properties:
        id:
          type: string
          format: uuid
        kararTuru:
          type: string
          enum:
          - "0"
          - "1"
          - "2"
          - "3"
          - "4"
          - "5"
          - "6"
          - "7"
          - "8"
          - "9"
        evrakDetay:
          $ref: '#/components/schemas/EvrakDetay'
        mahkemeKararBilgisi:
          $ref: '#/components/schemas/MahkemeKararBilgisi'
        mahkemeKararGuncellemeDetayListesi:
          maxItems: 2147483647
          minItems: 1
          type: array
          description: Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek
            yeni kod/il bilgileri
          items:
            $ref: '#/components/schemas/MahkemeKararGuncellemeDetay'
      description: Mahkeme Karar Detaylari
    MahkemeKararGuncellemeBilgi:
      required:
      - mahkemeKararGuncellemeAlan
      type: object
      properties:
        mahkemeKararGuncellemeAlan:
          type: string
          enum:
          - MAHKEME_KODU
          - SORUSTURMA_NO
          - MAHKEMEKARAR_NO
        yeniDegeri:
          type: string
    MahkemeKararGuncellemeDetay:
      required:
      - mahkemeKararDetay
      - mahkemeKararGuncellemeBilgiListesi
      type: object
      properties:
        mahkemeKararDetay:
          $ref: '#/components/schemas/MahkemeKararDetay'
        mahkemeKararGuncellemeBilgiListesi:
          maxItems: 2147483647
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/MahkemeKararGuncellemeBilgi'
      description: Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek
        yeni kod/il bilgileri
    IDMahkemeKararGuncellemeResponse:
      required:
      - evrakId
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        evrakId:
          type: integer
          format: int64
    HedefDetayIT:
      required:
      - baslamaTarihi
      - bitisTarihi
      - hedef
      - sorguTipi
      - tespitTuru
      type: object
      properties:
        sorguTipi:
          type: string
          enum:
          - "1"
          - "2"
          - "3"
        hedef:
          $ref: '#/components/schemas/Hedef'
        karsiHedef:
          $ref: '#/components/schemas/Hedef'
        baslamaTarihi:
          type: string
          format: date-time
        bitisTarihi:
          type: string
          format: date-time
        tespitTuru:
          type: string
        tespitTuruDetay:
          type: string
        aciklama:
          type: string
    ITKararRequest:
      required:
      - evrakDetay
      - hedefDetayListesi
      - id
      - kararTuru
      - mahkemeKararBilgisi
      type: object
      properties:
        id:
          type: string
          format: uuid
        kararTuru:
          type: string
          enum:
          - "0"
          - "1"
          - "2"
          - "3"
          - "4"
          - "5"
          - "6"
          - "7"
          - "8"
          - "9"
        evrakDetay:
          $ref: '#/components/schemas/EvrakDetay'
        mahkemeKararBilgisi:
          $ref: '#/components/schemas/MahkemeKararBilgisi'
        hedefDetayListesi:
          maxItems: 2147483647
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/HedefDetayIT'
      description: IT Mahkeme Karar Detaylari
    ITKararResponse:
      required:
      - evrakId
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        evrakId:
          type: integer
          format: int64
    HedefGuncellemeBilgi:
      required:
      - hedefGuncellemeAlan
      type: object
      properties:
        hedefGuncellemeAlan:
          type: string
          enum:
          - AD
          - SOYAD
          - HEDEF
          - TCKIMlIKNO
          - CANAK_NO
        yeniDegeri:
          type: string
    HedefGuncellemeDetay:
      required:
      - hedef
      - hedefGuncellemeBilgiListesi
      type: object
      properties:
        hedef:
          $ref: '#/components/schemas/Hedef'
        hedefGuncellemeBilgiListesi:
          maxItems: 2147483647
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/HedefGuncellemeBilgi'
    HedefGuncellemeKararDetay:
      required:
      - hedefGuncellemeDetayListesi
      - mahkemeKararDetay
      type: object
      properties:
        mahkemeKararDetay:
          $ref: '#/components/schemas/MahkemeKararDetay'
        hedefGuncellemeDetayListesi:
          maxItems: 2147483647
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/HedefGuncellemeDetay'
      description: "Güncelleme yapılacak hedefler için mahkeme karar bilgisi ve karara\
        \ ait güncellenecek ad, soyad bilgileri"
    IDHedefGuncellemeRequest:
      required:
      - evrakDetay
      - hedefGuncellemeKararDetayListesi
      - id
      - kararTuru
      - mahkemeKararBilgisi
      type: object
      properties:
        id:
          type: string
          format: uuid
        kararTuru:
          type: string
          enum:
          - "0"
          - "1"
          - "2"
          - "3"
          - "4"
          - "5"
          - "6"
          - "7"
          - "8"
          - "9"
        evrakDetay:
          $ref: '#/components/schemas/EvrakDetay'
        mahkemeKararBilgisi:
          $ref: '#/components/schemas/MahkemeKararBilgisi'
        hedefGuncellemeKararDetayListesi:
          maxItems: 2147483647
          minItems: 1
          type: array
          description: "Güncelleme yapılacak hedefler için mahkeme karar bilgisi ve\
            \ karara ait güncellenecek ad, soyad bilgileri"
          items:
            $ref: '#/components/schemas/HedefGuncellemeKararDetay'
      description: Mahkeme Karar Detaylari
    IDHedefGuncellemeResponse:
      required:
      - evrakId
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        evrakId:
          type: integer
          format: int64
    AidiyatGuncellemeDetay:
      required:
      - aidiyatKodu
      - guncellemeTip
      type: object
      properties:
        guncellemeTip:
          type: string
          enum:
          - "0"
          - "1"
        aidiyatKodu:
          type: string
    AidiyatGuncellemeKararDetay:
      required:
      - aidiyatGuncellemeDetayListesi
      - mahkemeKararDetay
      type: object
      properties:
        mahkemeKararDetay:
          $ref: '#/components/schemas/MahkemeKararDetay'
        aidiyatGuncellemeDetayListesi:
          maxItems: 2147483647
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/AidiyatGuncellemeDetay'
      description: Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek
        aidiyat bilgileri
    IDAidiyatBilgisiGuncellemeRequest:
      required:
      - aidiyatGuncellemeKararDetayListesi
      - evrakDetay
      - id
      - kararTuru
      - mahkemeKararBilgisi
      type: object
      properties:
        id:
          type: string
          format: uuid
        kararTuru:
          type: string
          enum:
          - "0"
          - "1"
          - "2"
          - "3"
          - "4"
          - "5"
          - "6"
          - "7"
          - "8"
          - "9"
        evrakDetay:
          $ref: '#/components/schemas/EvrakDetay'
        mahkemeKararBilgisi:
          $ref: '#/components/schemas/MahkemeKararBilgisi'
        aidiyatGuncellemeKararDetayListesi:
          maxItems: 2147483647
          minItems: 1
          type: array
          description: Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek
            aidiyat bilgileri
          items:
            $ref: '#/components/schemas/AidiyatGuncellemeKararDetay'
      description: Mahkeme Karar Detaylari
    IDAidiyatBilgisiGuncellemeResponse:
      required:
      - evrakId
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        evrakId:
          type: integer
          format: int64
    RegisterRequest:
      required:
      - kurum
      - password
      - role
      - userName
      type: object
      properties:
        userName:
          type: string
        password:
          type: string
        role:
          type: string
          enum:
          - ROLE_ADMIN
          - ROLE_QUERY_ADMIN
          - ROLE_KURUM_TEMSILCISI
          - ROLE_KURUM_KULLANICI
        kurum:
          type: string
          enum:
          - ADLI
          - EMNIYET
          - MIT
          - JANDARMA
          - BTK
          - EMNIYET_SIBER
    RegisterResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
    LoginRequest:
      required:
      - password
      - username
      type: object
      properties:
        username:
          type: string
        password:
          type: string
    LoginResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
        token:
          type: string
        userId:
          type: integer
          format: int64
        username:
          type: string
        actingUserName:
          type: string
        roles:
          uniqueItems: true
          type: array
          items:
            type: string
        kurum:
          type: string
          enum:
          - ADLI
          - EMNIYET
          - MIT
          - JANDARMA
          - BTK
          - EMNIYET_SIBER
    ChangePasswordRequest:
      required:
      - confirmPassword
      - currentPassword
      - newPassword
      type: object
      properties:
        currentPassword:
          type: string
        newPassword:
          type: string
        confirmPassword:
          type: string
    ChangePasswordResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
    GetUserResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
        user:
          $ref: '#/components/schemas/MakosUser'
    GetUserByIdResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
        user:
          $ref: '#/components/schemas/MakosUser'
    UsersListResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
        users:
          type: array
          items:
            $ref: '#/components/schemas/MakosUser'
    HealthCheckResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
    GetMakosUserAuditLogsByUsernameResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
        auditLogs:
          $ref: '#/components/schemas/PageMakosUserAuditLog'
    MakosUserAuditLog:
      type: object
      properties:
        id:
          type: string
          format: uuid
        userAuditType:
          type: string
          enum:
          - LOGIN
          - LOGOUT
          - IMPERSONATE_LOGIN
          - CHANGE_PASSWORD
          - GET_USERS_FOR_ADMIN
          - ACTIVATE
          - DEACTIVATE
          - ADD
          - UPDATE
          - DELETE
          - REGISTER
        username:
          type: string
        actingUsername:
          type: string
        userIp:
          type: string
        adminOperatedUsername:
          type: string
        requestTime:
          type: string
          format: date-time
        responseTime:
          type: string
          format: date-time
        responseCode:
          type: integer
          format: int32
    PageMakosUserAuditLog:
      type: object
      properties:
        totalElements:
          type: integer
          format: int64
        totalPages:
          type: integer
          format: int32
        first:
          type: boolean
        last:
          type: boolean
        size:
          type: integer
          format: int32
        content:
          type: array
          items:
            $ref: '#/components/schemas/MakosUserAuditLog'
        number:
          type: integer
          format: int32
        sort:
          $ref: '#/components/schemas/SortObject'
        numberOfElements:
          type: integer
          format: int32
        pageable:
          $ref: '#/components/schemas/PageableObject'
        empty:
          type: boolean
    PageableObject:
      type: object
      properties:
        offset:
          type: integer
          format: int64
        sort:
          $ref: '#/components/schemas/SortObject'
        unpaged:
          type: boolean
        paged:
          type: boolean
        pageNumber:
          type: integer
          format: int32
        pageSize:
          type: integer
          format: int32
    SortObject:
      type: object
      properties:
        empty:
          type: boolean
        unsorted:
          type: boolean
        sorted:
          type: boolean
    GetMakosUserAuditLogsByTypeResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
        auditLogs:
          $ref: '#/components/schemas/PageMakosUserAuditLog'
    GetMakosUserAuditLogListResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
        auditLogs:
          $ref: '#/components/schemas/PageMakosUserAuditLog'
    GetMakosUserAuditLogResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
        auditLog:
          $ref: '#/components/schemas/MakosUserAuditLog'
  securitySchemes:
    BasicAuth:
      type: http
      scheme: basic
