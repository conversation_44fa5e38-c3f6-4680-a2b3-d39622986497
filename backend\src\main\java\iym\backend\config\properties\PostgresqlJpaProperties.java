package iym.backend.config.properties;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;

import java.util.Map;

/**
 * PostgreSQL JPA Properties configuration that extends Spring Boot's default JpaProperties
 * Maps properties from application.properties with prefix 'spring.jpa.postgresql'
 * <p>
 * This approach preserves all Spring Boot defaults while allowing PostgreSQL-specific customizations
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Slf4j
@ConditionalOnProperty(
    name = "app.datasource.postgresql.enabled",
    havingValue = "true",
    matchIfMissing = true
)
public class PostgresqlJpaProperties extends JpaProperties {
    public PostgresqlJpaProperties() {
        // Set default PostgreSQL dialect if not already configured
        // This can be overridden by @ConfigurationProperties from application.properties
        if (!this.getProperties().containsKey("hibernate.dialect")) {
            this.getProperties().put("hibernate.dialect", "org.hibernate.dialect.PostgreSQLDialect");
        }
    }

    /**
     * Get all JPA properties as a Map for EntityManagerFactory
     * Inherits all Spring Boot defaults and adds PostgreSQL-specific properties
     */
    @Override
    public Map<String, String> getProperties() {
        Map<String, String> properties = super.getProperties();

        log.info("PostgreSQL JPA Properties - Before applying defaults:");
        properties.forEach((key, value) -> {
            if (key.contains("naming") || key.contains("dialect")) {
                log.info("  {} = {}", key, value);
            }
        });

        // Apply PostgreSQL-specific defaults
        applyPostgresqlDefaults(properties);

        log.info("PostgreSQL JPA Properties - After applying defaults:");
        properties.forEach((key, value) -> {
            if (key.contains("naming") || key.contains("dialect")) {
                log.info("  {} = {}", key, value);
            }
        });

        return properties;
    }

    /**
     * Apply PostgreSQL-specific default configurations
     * <p>
     * ANALYSIS: Multi-datasource setup disables Spring Boot's auto-configuration,
     * which means JpaProperties defaults are not automatically applied.
     * This method manually applies Spring Boot's default JPA settings for PostgreSQL.
     * <p>
     * This is equivalent to what Spring Boot's JpaProperties auto-configuration would do
     * if it wasn't disabled by multi-datasource setup.
     */
    private void applyPostgresqlDefaults(Map<String, String> properties) {
        log.info("Applying PostgreSQL defaults (manual Spring Boot auto-configuration simulation)...");

        // Check if H2 dialect is being used (test environment)
        String dialect = properties.get("hibernate.dialect");
        boolean isH2Dialect = dialect != null && dialect.contains("H2");

        if (isH2Dialect) {
            log.info("H2 dialect detected in test environment, skipping PostgreSQL-specific defaults");
            return;
        }

        log.info("PostgreSQL dialect detected, simulating Spring Boot's JpaProperties auto-configuration");

        // Simulate Spring Boot's JpaProperties.getHibernateProperties() for PostgreSQL
        // These are the exact same defaults that Spring Boot applies automatically
        applySpringBootJpaDefaults(properties);

        // Apply PostgreSQL-specific optimizations
        applyPostgreSQLOptimizations(properties);
    }

    /**
     * Apply Spring Boot's default JPA properties that would normally be set by auto-configuration
     * This simulates org.springframework.boot.autoconfigure.orm.jpa.JpaProperties behavior
     */
    private void applySpringBootJpaDefaults(Map<String, String> properties) {
        log.info("Applying Spring Boot JPA defaults...");

        // Spring Boot's default naming strategies (Spring Boot 3.x compatible)
        if (!properties.containsKey("hibernate.physical_naming_strategy")) {
            properties.put("hibernate.physical_naming_strategy",
                    "org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy");
            log.info("Applied Spring Boot default physical naming strategy: CamelCaseToUnderscoresNamingStrategy");
        }

        if (!properties.containsKey("hibernate.implicit_naming_strategy")) {
            properties.put("hibernate.implicit_naming_strategy",
                    "org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy");
            log.info("Applied Spring Boot default implicit naming strategy: SpringImplicitNamingStrategy");
        }

        // Other Spring Boot JPA defaults
        if (!properties.containsKey("hibernate.id.new_generator_mappings")) {
            properties.put("hibernate.id.new_generator_mappings", "true");
            log.info("Applied Spring Boot default: new_generator_mappings=true");
        }
    }

    /**
     * Apply PostgreSQL-specific optimizations
     */
    private void applyPostgreSQLOptimizations(Map<String, String> properties) {
        log.info("Applying PostgreSQL-specific optimizations...");

        if (!properties.containsKey("hibernate.temp.use_jdbc_metadata_defaults")) {
            properties.put("hibernate.temp.use_jdbc_metadata_defaults", "false");
            log.info("Applied PostgreSQL optimization: use_jdbc_metadata_defaults=false");
        }

        if (!properties.containsKey("hibernate.jdbc.lob.non_contextual_creation")) {
            properties.put("hibernate.jdbc.lob.non_contextual_creation", "true");
            log.info("Applied PostgreSQL optimization: lob.non_contextual_creation=true");
        }
    }
}
