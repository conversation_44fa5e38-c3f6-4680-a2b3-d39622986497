package iym.makos.dto.id;

import iym.common.model.api.KararTuru;
import iym.common.model.api.MahkemeKararTip;
import iym.common.util.CommonUtils;
import iym.common.validation.ValidationResult;
import iym.makos.model.api.IDHedefDetay;
import iym.makos.model.reqrep.MahkemeKararRequest;
import iym.makos.validator.MakosRequestValid;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Jacksonized
@Data
@NoArgsConstructor
@SuperBuilder
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@MakosRequestValid
@Slf4j
public class IDUzatmaKarariRequest extends MahkemeKararRequest {

    //@Schema(description = "Uzatilan/Sonlandirilan Hedefin ilgili Mahkeme Kararı. Sadece uzatma/sonlandirma kararlarinda gerekli")
    //private MahkemeKararDetay ilgiliMahkemeKararDetayi;

    @NotNull
    @Size(min = 1)
    @Valid
    private List<IDHedefDetay> hedefDetayListesi;

    //TODO: Uzatma kararında Aidiyat ve suc tipi olacak mı?
    private List<String> mahkemeAidiyatKodlari;
    private List<String> mahkemeSucTipiKodlari;

    @Override
    public ValidationResult isValid() {
        log.trace("Checking if IDUzatmaKarariRequest is valid");

        try {
            ValidationResult validationResult = new ValidationResult(true);

            if (kararTuru != KararTuru.ILETISIMIN_DENETLENMESI_UZATMA_KARARI) {
                validationResult.addFailedReason("Karar türü: " + KararTuru.ILETISIMIN_DENETLENMESI_UZATMA_KARARI.name() + " olmalıdır");
                return validationResult;
            }

            MahkemeKararTip mahkemeKararTipi = mahkemeKararBilgisi.getMahkemeKararTipi();
            boolean uzatmaMahkemeKararTipinde = CommonUtils.uzatmaMahkemeKararTipi(mahkemeKararTipi);
            if (!uzatmaMahkemeKararTipinde) {
                validationResult.addFailedReason("Mahkeme karari tipi, uzatma kararı için uygun değildir!");
            }

            for (IDHedefDetay IDHedefDetay : hedefDetayListesi) {
                if (IDHedefDetay.getIlgiliMahkemeKararDetayi() == null) {
                    validationResult.addFailedReason("Uzatma Kararinda ilgili mahkeme karari bos olamaz!");
                }

                if (!CommonUtils.isNullOrEmpty(IDHedefDetay.getCanakNo())) {
                    validationResult.addFailedReason("Uzatma kararında CANAK numarası girilemez.");
                }

                if (IDHedefDetay.getUzatmaSayisi() == null) {
                    validationResult.addFailedReason("Uzatma Kararinda uzatma sayisi boş olamaz!");
                } else if (IDHedefDetay.getUzatmaSayisi() < 0) {
                    validationResult.addFailedReason("Uzatma Kararinda uzatma sayisi sıfırdan küçük olamaz!");
                }
            }
            return validationResult;
        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }

    }

    @Override
    protected void assignKararTuru() {
        this.kararTuru = KararTuru.ILETISIMIN_DENETLENMESI_UZATMA_KARARI;
    }
}

