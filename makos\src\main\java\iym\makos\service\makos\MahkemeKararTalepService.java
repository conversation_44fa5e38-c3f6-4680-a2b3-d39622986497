package iym.makos.service.makos;

import iym.common.model.api.*;
import iym.common.model.entity.iym.*;
import iym.common.model.enums.ResponseCode;
import iym.common.service.db.DbEvrakKayitService;
import iym.common.service.db.DbMahkemeKararTalepService;
import iym.common.util.CommonUtils;
import iym.db.jpa.dao.*;
import iym.makos.dto.MahkemeKararTalepDTO;
import iym.makos.dto.id.*;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.handler.MahkemeKararRequestDBSaveHandlerFactory;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.mapper.MahkemeKararTalepMapper;
import iym.makos.model.api.*;
import iym.makos.model.reqrep.MahkemeKararRequest;
import iym.makos.utils.UtilService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Service for MahkemeKararTalep operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MahkemeKararTalepService {
    private  final UtilService utilService;

    private final DbMahkemeKararTalepService dbMahkemeKararTalepService;
    private final DbEvrakKayitService dbEvrakKayitService;
    private final MahkemeKararTalepMapper mahkemeKararTalepMapper;

    private final KararRequestMapper kararRequestMapper;

    private final EvrakKayitRepo evrakKayitReporo;
    private final MahkemeBilgisiRepo mahkemeBilgisiRepo;
    private final MahkemeKararTalepRepo mahkemeKararTalepRepo;
    private final MahkemeSuclarTalepRepo mahkemeSuclarTalepRepo;
    private final MahkemeAidiyatTalepRepo mahkemeAidiyatTalepRepo;
    private final HedeflerTalepRepo hedeflerTalepRepo;
    private final HedeflerAidiyatTalepRepo hedeflerAidiyatTalepRepo;
    private final DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo;
    //
    private final MahkemeKararRepo mahkemeKararRepo;
    private final HedeflerRepo hedeflerRepo;
    private final HedeflerDetayTalepRepo hedeflerDetayTalepRepo;
    private final MahkemeAidiyatDetayTalepRepo mahkemeAidiyatDetayTalepRepo;

    private final MahkemeKararRequestDBSaveHandlerFactory mahkemeKararRequestDBSaveHandlerFactory;

    /**
     * Get all mahkeme karar talep records
     * @return List of MahkemeKararTalepDTO
     */



    public List<MahkemeKararTalepDTO> findAll() {
        List<MahkemeKararTalep> mahkemeKararTalepList = dbMahkemeKararTalepService.findAll();
        return mahkemeKararTalepMapper.toDtoList(mahkemeKararTalepList);
    }

    /**
     * Get mahkeme karar talep by ID
     * @param id Mahkeme karar talep ID
     * @return MahkemeKararTalepDTO
     * @throws ResponseStatusException if not found
     */

    public MahkemeKararTalepDTO findById(Long id) {
        return dbMahkemeKararTalepService.findById(id)
                .map(mahkemeKararTalepMapper::toDto)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme karar talep bulunamadı: " + id));
    }

    /**
     * Get mahkeme karar talep records by evrak ID
     * @param evrakId Evrak ID
     * @return List of MahkemeKararTalepDTO
     */

    public List<MahkemeKararTalepDTO> findByEvrakId(Long evrakId) {
        List<MahkemeKararTalep> mahkemeKararTalepList = dbMahkemeKararTalepService.findByEvrakId(evrakId);
        return mahkemeKararTalepMapper.toDtoList(mahkemeKararTalepList);
    }


    /**
     * Get paginated mahkeme karar talep records
     * @param pageable Pageable
     * @return Page of MahkemeKararTalepDTO
     */

    public Page<MahkemeKararTalepDTO> findAll(Pageable pageable) {
        Page<MahkemeKararTalep> mahkemeKararTalepPage = dbMahkemeKararTalepService.findAll(pageable);
        List<MahkemeKararTalepDTO> dtoList = mahkemeKararTalepMapper.toDtoList(mahkemeKararTalepPage.getContent());
        return new PageImpl<>(dtoList, pageable, mahkemeKararTalepPage.getTotalElements());
    }

    /**
     * Create new mahkeme karar talep
     * @param mahkemeKararTalepDTO MahkemeKararTalepDTO
     * @return Created MahkemeKararTalepDTO
     */

    public MahkemeKararTalepDTO create(MahkemeKararTalepDTO mahkemeKararTalepDTO) {
        // Check if mahkeme karar already exists
        if (mahkemeKararTalepDTO.getMahkemeKararNo() != null &&
            mahkemeKararTalepDTO.getMahkemeAdi() != null &&
            mahkemeKararTalepDTO.getMahkemeIlIlceKodu() != null) {
            // TODO
        }

        MahkemeKararTalep mahkemeKararTalep = mahkemeKararTalepMapper.toEntity(mahkemeKararTalepDTO);
        dbMahkemeKararTalepService.save(mahkemeKararTalep);
        log.info("Mahkeme karar talep oluşturuldu: {}", mahkemeKararTalep.getId());
        return mahkemeKararTalepMapper.toDto(mahkemeKararTalep);
    }

    /**
     * Update mahkeme karar talep
     * @param id Mahkeme karar talep ID
     * @param mahkemeKararTalepDTO MahkemeKararTalepDTO
     * @return Updated MahkemeKararTalepDTO
     */

    public MahkemeKararTalepDTO update(Long id, MahkemeKararTalepDTO mahkemeKararTalepDTO) {
        MahkemeKararTalep existingMahkemeKararTalep = dbMahkemeKararTalepService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme karar talep bulunamadı: " + id));

        // Check if mahkeme karar is being changed and already exists
        if (mahkemeKararTalepDTO.getMahkemeKararNo() != null &&
            mahkemeKararTalepDTO.getMahkemeAdi() != null &&
            mahkemeKararTalepDTO.getMahkemeIlIlceKodu() != null &&
            (!mahkemeKararTalepDTO.getMahkemeKararNo().equals(existingMahkemeKararTalep.getMahkemeKararNo()) ||
             !mahkemeKararTalepDTO.getMahkemeAdi().equals(existingMahkemeKararTalep.getMahkemeAdi()) ||
             !mahkemeKararTalepDTO.getMahkemeIlIlceKodu().equals(existingMahkemeKararTalep.getMahkemeIlIlceKodu()))) {

            // TODO
        }

        MahkemeKararTalep updatedMahkemeKararTalep = mahkemeKararTalepMapper.updateEntityFromDto(existingMahkemeKararTalep, mahkemeKararTalepDTO);
        dbMahkemeKararTalepService.update(updatedMahkemeKararTalep);
        log.info("Mahkeme karar talep güncellendi: {}", updatedMahkemeKararTalep.getId());
        return mahkemeKararTalepMapper.toDto(updatedMahkemeKararTalep);
    }

    /**
     * Delete mahkeme karar talep
     * @param id Mahkeme karar talep ID
     */

    public void delete(Long id) {
        MahkemeKararTalep mahkemeKararTalep = dbMahkemeKararTalepService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme karar talep bulunamadı: " + id));

        dbMahkemeKararTalepService.delete(mahkemeKararTalep);
        log.info("Mahkeme karar talep silindi: {}", id);
    }
}
