/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import iym.makos.api.client.gen.model.ModelApiResponse;
import java.util.LinkedHashSet;
import java.util.Set;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * LoginResponse
 */
@JsonPropertyOrder({
  LoginResponse.JSON_PROPERTY_RESPONSE,
  LoginResponse.JSON_PROPERTY_TOKEN,
  LoginResponse.JSON_PROPERTY_USER_ID,
  LoginResponse.JSON_PROPERTY_USERNAME,
  LoginResponse.JSON_PROPERTY_ACTING_USER_NAME,
  LoginResponse.JSON_PROPERTY_ROLES,
  LoginResponse.JSON_PROPERTY_KURUM
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class LoginResponse {
  public static final String JSON_PROPERTY_RESPONSE = "response";
  private ModelApiResponse response;

  public static final String JSON_PROPERTY_TOKEN = "token";
  private String token;

  public static final String JSON_PROPERTY_USER_ID = "userId";
  private Long userId;

  public static final String JSON_PROPERTY_USERNAME = "username";
  private String username;

  public static final String JSON_PROPERTY_ACTING_USER_NAME = "actingUserName";
  private String actingUserName;

  public static final String JSON_PROPERTY_ROLES = "roles";
  private Set<String> roles;

  /**
   * Gets or Sets kurum
   */
  public enum KurumEnum {
    ADLI("ADLI"),
    
    EMNIYET("EMNIYET"),
    
    MIT("MIT"),
    
    JANDARMA("JANDARMA"),
    
    BTK("BTK"),
    
    EMNIYET_SIBER("EMNIYET_SIBER");

    private String value;

    KurumEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static KurumEnum fromValue(String value) {
      for (KurumEnum b : KurumEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_KURUM = "kurum";
  private KurumEnum kurum;

  public LoginResponse() {
  }

  public LoginResponse response(ModelApiResponse response) {
    
    this.response = response;
    return this;
  }

   /**
   * Get response
   * @return response
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_RESPONSE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public ModelApiResponse getResponse() {
    return response;
  }


  @JsonProperty(JSON_PROPERTY_RESPONSE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setResponse(ModelApiResponse response) {
    this.response = response;
  }


  public LoginResponse token(String token) {
    
    this.token = token;
    return this;
  }

   /**
   * Get token
   * @return token
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TOKEN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getToken() {
    return token;
  }


  @JsonProperty(JSON_PROPERTY_TOKEN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setToken(String token) {
    this.token = token;
  }


  public LoginResponse userId(Long userId) {
    
    this.userId = userId;
    return this;
  }

   /**
   * Get userId
   * @return userId
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_USER_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Long getUserId() {
    return userId;
  }


  @JsonProperty(JSON_PROPERTY_USER_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUserId(Long userId) {
    this.userId = userId;
  }


  public LoginResponse username(String username) {
    
    this.username = username;
    return this;
  }

   /**
   * Get username
   * @return username
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_USERNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getUsername() {
    return username;
  }


  @JsonProperty(JSON_PROPERTY_USERNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUsername(String username) {
    this.username = username;
  }


  public LoginResponse actingUserName(String actingUserName) {
    
    this.actingUserName = actingUserName;
    return this;
  }

   /**
   * Get actingUserName
   * @return actingUserName
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ACTING_USER_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getActingUserName() {
    return actingUserName;
  }


  @JsonProperty(JSON_PROPERTY_ACTING_USER_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setActingUserName(String actingUserName) {
    this.actingUserName = actingUserName;
  }


  public LoginResponse roles(Set<String> roles) {
    
    this.roles = roles;
    return this;
  }

  public LoginResponse addRolesItem(String rolesItem) {
    if (this.roles == null) {
      this.roles = new LinkedHashSet<>();
    }
    this.roles.add(rolesItem);
    return this;
  }

   /**
   * Get roles
   * @return roles
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ROLES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Set<String> getRoles() {
    return roles;
  }


  @JsonDeserialize(as = LinkedHashSet.class)
  @JsonProperty(JSON_PROPERTY_ROLES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRoles(Set<String> roles) {
    this.roles = roles;
  }


  public LoginResponse kurum(KurumEnum kurum) {
    
    this.kurum = kurum;
    return this;
  }

   /**
   * Get kurum
   * @return kurum
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_KURUM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public KurumEnum getKurum() {
    return kurum;
  }


  @JsonProperty(JSON_PROPERTY_KURUM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setKurum(KurumEnum kurum) {
    this.kurum = kurum;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    LoginResponse loginResponse = (LoginResponse) o;
    return Objects.equals(this.response, loginResponse.response) &&
        Objects.equals(this.token, loginResponse.token) &&
        Objects.equals(this.userId, loginResponse.userId) &&
        Objects.equals(this.username, loginResponse.username) &&
        Objects.equals(this.actingUserName, loginResponse.actingUserName) &&
        Objects.equals(this.roles, loginResponse.roles) &&
        Objects.equals(this.kurum, loginResponse.kurum);
  }

  @Override
  public int hashCode() {
    return Objects.hash(response, token, userId, username, actingUserName, roles, kurum);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class LoginResponse {\n");
    sb.append("    response: ").append(toIndentedString(response)).append("\n");
    sb.append("    token: ").append(toIndentedString(token)).append("\n");
    sb.append("    userId: ").append(toIndentedString(userId)).append("\n");
    sb.append("    username: ").append(toIndentedString(username)).append("\n");
    sb.append("    actingUserName: ").append(toIndentedString(actingUserName)).append("\n");
    sb.append("    roles: ").append(toIndentedString(roles)).append("\n");
    sb.append("    kurum: ").append(toIndentedString(kurum)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

