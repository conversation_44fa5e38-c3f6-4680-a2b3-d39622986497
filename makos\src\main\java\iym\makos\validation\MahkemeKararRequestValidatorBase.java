package iym.makos.validation;

import iym.common.validation.ValidationResult;
import iym.makos.model.reqrep.MahkemeKararRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.GenericTypeResolver;

@Slf4j
public abstract class MahkemeKararRequestValidatorBase<T extends MahkemeKararRequest> implements IMahkemeKararRequestValidator<T> {

    protected MahkemeKararRequestCommonValidator mahkemeKararRequestCommonValidator;

    @Autowired
    public final void setMahkemeKararRequestCommonValidator(MahkemeKararRequestCommonValidator mahkemeKararRequestCommonValidator) {
        this.mahkemeKararRequestCommonValidator = mahkemeKararRequestCommonValidator;
    }

    @Override
    public ValidationResult validate(T request) {

        try{
            // check the request object
            ValidationResult validationResult = request.isValid();

            // if request is not valid, no need to continue validation
            if (!validationResult.isValid()) {
                return validationResult;
            }

            // check the MahkemeKararRequest common part
            validationResult = mahkemeKararRequestCommonValidator.validate(request);

            // if MahkemeKararRequest part is not valid, no need to continue validation
            if (!validationResult.isValid()) {
                return validationResult;
            }

            return validationResult;
        }
        catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }
    }

    @SuppressWarnings("unchecked")
    public Class<T> getRelatedRequestType() {
        return (Class<T>) GenericTypeResolver.resolveTypeArgument(
                this.getClass(),
                IMahkemeKararRequestValidator.class
        );
    }

}

