package iym.makos.model.reqrep;

import iym.makos.model.MakosApiResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import java.util.UUID;

@Data
@SuperBuilder
@Jacksonized
@ToString
@EqualsAndHashCode
public class MahkemeKararResponse {

  @NotNull
  private UUID requestId;

  @NotNull
  @Valid
  private MakosApiResponse response;

}