package iym.backend.config;

import iym.backend.config.properties.OracleJpaProperties;
import jakarta.persistence.EntityManagerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

/**
 * Oracle DataSource configuration for primary database
 */
@Configuration
@EnableTransactionManagement
@ComponentScan("iym.db")
@EnableJpaRepositories(
        basePackages = "iym.db",
        entityManagerFactoryRef = "oracleEntityManagerFactory",
        transactionManagerRef = "oracleTransactionManager"
)
public class OracleDataSourceConfig {

    private final OracleJpaProperties oracleJpaProperties;

    /**
     * Constructor injection for better dependency management
     *
     * @param oracleJpaProperties Oracle JPA configuration properties
     */
    public OracleDataSourceConfig(@Qualifier("oracleJpaProperties") OracleJpaProperties oracleJpaProperties) {
        this.oracleJpaProperties = oracleJpaProperties;
    }

    @Bean(name = "oracleDataSource")
    @ConfigurationProperties("spring.datasource.oracle")
    public DataSource oracleDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "oracleEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean oracleEntityManagerFactory(
            DataSource oracleDataSource) {

        LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
        em.setDataSource(oracleDataSource);
        em.setPackagesToScan("iym.common.model.entity");

        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        vendorAdapter.setDatabasePlatform("org.hibernate.dialect.OracleDialect");
        em.setJpaVendorAdapter(vendorAdapter);

        // Use Spring Boot's default JPA properties + Oracle-specific customizations
        em.setJpaPropertyMap(oracleJpaProperties.getProperties());

        return em;
    }

    @Bean(name = "oracleTransactionManager")
    public PlatformTransactionManager oracleTransactionManager(
            EntityManagerFactory oracleEntityManagerFactory) {
        return new JpaTransactionManager(oracleEntityManagerFactory);
    }
}
