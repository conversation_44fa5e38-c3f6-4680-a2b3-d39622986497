/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * EvrakDetay
 */
@JsonPropertyOrder({
  EvrakDetay.JSON_PROPERTY_EVRAK_NO,
  EvrakDetay.JSON_PROPERTY_EVRAK_TARIHI,
  EvrakDetay.JSON_PROPERTY_EVRAK_KURUM_KODU,
  EvrakDetay.JSON_PROPERTY_EVRAK_TURU,
  EvrakDetay.JSON_PROPERTY_HAVALE_BIRIMI,
  EvrakDetay.JSON_PROPERTY_ACIKLAMA,
  EvrakDetay.JSON_PROPERTY_GELDIGI_IL_ILCE_KODU,
  EvrakDetay.JSON_PROPERTY_ACILMI,
  EvrakDetay.JSON_PROPERTY_EVRAK_KONUSU
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class EvrakDetay {
  public static final String JSON_PROPERTY_EVRAK_NO = "evrakNo";
  private String evrakNo;

  public static final String JSON_PROPERTY_EVRAK_TARIHI = "evrakTarihi";
  private LocalDateTime evrakTarihi;

  public static final String JSON_PROPERTY_EVRAK_KURUM_KODU = "evrakKurumKodu";
  private String evrakKurumKodu;

  /**
   * Gets or Sets evrakTuru
   */
  public enum EvrakTuruEnum {
    _0("0"),
    
    _1("1"),
    
    _2("2");

    private String value;

    EvrakTuruEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static EvrakTuruEnum fromValue(String value) {
      for (EvrakTuruEnum b : EvrakTuruEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_EVRAK_TURU = "evrakTuru";
  private EvrakTuruEnum evrakTuru;

  public static final String JSON_PROPERTY_HAVALE_BIRIMI = "havaleBirimi";
  private String havaleBirimi;

  public static final String JSON_PROPERTY_ACIKLAMA = "aciklama";
  private String aciklama;

  public static final String JSON_PROPERTY_GELDIGI_IL_ILCE_KODU = "geldigiIlIlceKodu";
  private String geldigiIlIlceKodu;

  public static final String JSON_PROPERTY_ACILMI = "acilmi";
  private Boolean acilmi;

  public static final String JSON_PROPERTY_EVRAK_KONUSU = "evrakKonusu";
  private String evrakKonusu;

  public EvrakDetay() {
  }

  public EvrakDetay evrakNo(String evrakNo) {
    
    this.evrakNo = evrakNo;
    return this;
  }

   /**
   * Get evrakNo
   * @return evrakNo
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_EVRAK_NO)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getEvrakNo() {
    return evrakNo;
  }


  @JsonProperty(JSON_PROPERTY_EVRAK_NO)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setEvrakNo(String evrakNo) {
    this.evrakNo = evrakNo;
  }


  public EvrakDetay evrakTarihi(LocalDateTime evrakTarihi) {
    
    this.evrakTarihi = evrakTarihi;
    return this;
  }

   /**
   * Get evrakTarihi
   * @return evrakTarihi
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_EVRAK_TARIHI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public LocalDateTime getEvrakTarihi() {
    return evrakTarihi;
  }


  @JsonProperty(JSON_PROPERTY_EVRAK_TARIHI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setEvrakTarihi(LocalDateTime evrakTarihi) {
    this.evrakTarihi = evrakTarihi;
  }


  public EvrakDetay evrakKurumKodu(String evrakKurumKodu) {
    
    this.evrakKurumKodu = evrakKurumKodu;
    return this;
  }

   /**
   * Get evrakKurumKodu
   * @return evrakKurumKodu
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_EVRAK_KURUM_KODU)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getEvrakKurumKodu() {
    return evrakKurumKodu;
  }


  @JsonProperty(JSON_PROPERTY_EVRAK_KURUM_KODU)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setEvrakKurumKodu(String evrakKurumKodu) {
    this.evrakKurumKodu = evrakKurumKodu;
  }


  public EvrakDetay evrakTuru(EvrakTuruEnum evrakTuru) {
    
    this.evrakTuru = evrakTuru;
    return this;
  }

   /**
   * Get evrakTuru
   * @return evrakTuru
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_EVRAK_TURU)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public EvrakTuruEnum getEvrakTuru() {
    return evrakTuru;
  }


  @JsonProperty(JSON_PROPERTY_EVRAK_TURU)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setEvrakTuru(EvrakTuruEnum evrakTuru) {
    this.evrakTuru = evrakTuru;
  }


  public EvrakDetay havaleBirimi(String havaleBirimi) {
    
    this.havaleBirimi = havaleBirimi;
    return this;
  }

   /**
   * Get havaleBirimi
   * @return havaleBirimi
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAVALE_BIRIMI)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getHavaleBirimi() {
    return havaleBirimi;
  }


  @JsonProperty(JSON_PROPERTY_HAVALE_BIRIMI)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHavaleBirimi(String havaleBirimi) {
    this.havaleBirimi = havaleBirimi;
  }


  public EvrakDetay aciklama(String aciklama) {
    
    this.aciklama = aciklama;
    return this;
  }

   /**
   * Get aciklama
   * @return aciklama
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ACIKLAMA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAciklama() {
    return aciklama;
  }


  @JsonProperty(JSON_PROPERTY_ACIKLAMA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAciklama(String aciklama) {
    this.aciklama = aciklama;
  }


  public EvrakDetay geldigiIlIlceKodu(String geldigiIlIlceKodu) {
    
    this.geldigiIlIlceKodu = geldigiIlIlceKodu;
    return this;
  }

   /**
   * Get geldigiIlIlceKodu
   * @return geldigiIlIlceKodu
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_GELDIGI_IL_ILCE_KODU)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getGeldigiIlIlceKodu() {
    return geldigiIlIlceKodu;
  }


  @JsonProperty(JSON_PROPERTY_GELDIGI_IL_ILCE_KODU)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setGeldigiIlIlceKodu(String geldigiIlIlceKodu) {
    this.geldigiIlIlceKodu = geldigiIlIlceKodu;
  }


  public EvrakDetay acilmi(Boolean acilmi) {
    
    this.acilmi = acilmi;
    return this;
  }

   /**
   * Get acilmi
   * @return acilmi
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ACILMI)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getAcilmi() {
    return acilmi;
  }


  @JsonProperty(JSON_PROPERTY_ACILMI)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAcilmi(Boolean acilmi) {
    this.acilmi = acilmi;
  }


  public EvrakDetay evrakKonusu(String evrakKonusu) {
    
    this.evrakKonusu = evrakKonusu;
    return this;
  }

   /**
   * Get evrakKonusu
   * @return evrakKonusu
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EVRAK_KONUSU)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getEvrakKonusu() {
    return evrakKonusu;
  }


  @JsonProperty(JSON_PROPERTY_EVRAK_KONUSU)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEvrakKonusu(String evrakKonusu) {
    this.evrakKonusu = evrakKonusu;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    EvrakDetay evrakDetay = (EvrakDetay) o;
    return Objects.equals(this.evrakNo, evrakDetay.evrakNo) &&
        Objects.equals(this.evrakTarihi, evrakDetay.evrakTarihi) &&
        Objects.equals(this.evrakKurumKodu, evrakDetay.evrakKurumKodu) &&
        Objects.equals(this.evrakTuru, evrakDetay.evrakTuru) &&
        Objects.equals(this.havaleBirimi, evrakDetay.havaleBirimi) &&
        Objects.equals(this.aciklama, evrakDetay.aciklama) &&
        Objects.equals(this.geldigiIlIlceKodu, evrakDetay.geldigiIlIlceKodu) &&
        Objects.equals(this.acilmi, evrakDetay.acilmi) &&
        Objects.equals(this.evrakKonusu, evrakDetay.evrakKonusu);
  }

  @Override
  public int hashCode() {
    return Objects.hash(evrakNo, evrakTarihi, evrakKurumKodu, evrakTuru, havaleBirimi, aciklama, geldigiIlIlceKodu, acilmi, evrakKonusu);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class EvrakDetay {\n");
    sb.append("    evrakNo: ").append(toIndentedString(evrakNo)).append("\n");
    sb.append("    evrakTarihi: ").append(toIndentedString(evrakTarihi)).append("\n");
    sb.append("    evrakKurumKodu: ").append(toIndentedString(evrakKurumKodu)).append("\n");
    sb.append("    evrakTuru: ").append(toIndentedString(evrakTuru)).append("\n");
    sb.append("    havaleBirimi: ").append(toIndentedString(havaleBirimi)).append("\n");
    sb.append("    aciklama: ").append(toIndentedString(aciklama)).append("\n");
    sb.append("    geldigiIlIlceKodu: ").append(toIndentedString(geldigiIlIlceKodu)).append("\n");
    sb.append("    acilmi: ").append(toIndentedString(acilmi)).append("\n");
    sb.append("    evrakKonusu: ").append(toIndentedString(evrakKonusu)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

