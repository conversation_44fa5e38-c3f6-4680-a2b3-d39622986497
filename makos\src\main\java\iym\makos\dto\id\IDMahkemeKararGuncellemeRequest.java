package iym.makos.dto.id;

import io.swagger.v3.oas.annotations.media.Schema;
import iym.common.model.api.KararTuru;
import iym.common.model.api.MahkemeKararTip;
import iym.common.validation.ValidationResult;
import iym.makos.model.api.MahkemeKararDetay;
import iym.makos.model.api.MahkemeKararGuncellemeDetay;
import iym.makos.model.reqrep.MahkemeKararRequest;
import iym.makos.validator.MakosRequestValid;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Jacksonized
@Data
@NoArgsConstructor
@SuperBuilder
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@MakosRequestValid
@Slf4j
public class IDMahkemeKararGuncellemeRequest extends MahkemeKararRequest {

    @NotNull
    @Valid
    @Size(min = 1)
    @Schema(description = "Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek yeni kod/il bilgileri")
    private List<MahkemeKararGuncellemeDetay> mahkemeKararGuncellemeDetayListesi;

    @Override
    public ValidationResult isValid() {
        log.trace("Checking if MahkemeBilgisiGuncellemeRequest is valid");

        try {
            ValidationResult validationResult = new ValidationResult(true);

            if (kararTuru != KararTuru.ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME) {
                validationResult.addFailedReason("Karar türü: " + KararTuru.ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME.name() + " olmalıdır");
                return validationResult;
            }

            MahkemeKararTip kararTip = mahkemeKararBilgisi.getMahkemeKararTipi();
            if (kararTip != MahkemeKararTip.MAHKEME_KARAR_BILGI_DEGISTIRME) {
                validationResult.addFailedReason("Mahkeme karar Tipi " + MahkemeKararTip.MAHKEME_KARAR_BILGI_DEGISTIRME.name() + " olmalıdır");
            }
            for (MahkemeKararGuncellemeDetay mahkemeKararGuncellemeDetay : mahkemeKararGuncellemeDetayListesi) {

                MahkemeKararDetay iliskiliMahkemeKararDetay = mahkemeKararGuncellemeDetay.getMahkemeKararDetay();
                if (iliskiliMahkemeKararDetay == null) {
                    validationResult.addFailedReason("Güncellemeye konu mahkeme karar bilgileri boş olamaz.!");
                }

            }

            return validationResult;
        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }

    }

    @Override
    protected void assignKararTuru() {
        this.kararTuru = KararTuru.ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME;
    }
}

