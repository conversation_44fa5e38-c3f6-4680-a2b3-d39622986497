package iym.makos.handler;

import iym.makos.model.reqrep.MahkemeKararRequest;

import java.util.Date;


public interface MahkemeKararDBSaveHandler<T extends MahkemeKararRequest> {
    Long kaydet(T request, Date kayit<PERSON><PERSON><PERSON>, Long kullaniciId) throws  Exception;
   // boolean onayla(Long mahkemeKararTalepId, Date onayTarihi, Long kullaniciId) throws  Exception;
    Class<T> getRelatedRequestType();
}

