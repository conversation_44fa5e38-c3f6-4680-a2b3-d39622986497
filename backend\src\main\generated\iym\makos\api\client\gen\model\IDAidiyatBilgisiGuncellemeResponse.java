/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import iym.makos.api.client.gen.model.MakosApiResponse;
import java.util.UUID;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * IDAidiyatBilgisiGuncellemeResponse
 */
@JsonPropertyOrder({
  IDAidiyatBilgisiGuncellemeResponse.JSON_PROPERTY_REQUEST_ID,
  IDAidiyatBilgisiGuncellemeResponse.JSON_PROPERTY_RESPONSE,
  IDAidiyatBilgisiGuncellemeResponse.JSON_PROPERTY_EVRAK_ID
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class IDAidiyatBilgisiGuncellemeResponse {
  public static final String JSON_PROPERTY_REQUEST_ID = "requestId";
  private UUID requestId;

  public static final String JSON_PROPERTY_RESPONSE = "response";
  private MakosApiResponse response;

  public static final String JSON_PROPERTY_EVRAK_ID = "evrakId";
  private Long evrakId;

  public IDAidiyatBilgisiGuncellemeResponse() {
  }

  public IDAidiyatBilgisiGuncellemeResponse requestId(UUID requestId) {
    
    this.requestId = requestId;
    return this;
  }

   /**
   * Get requestId
   * @return requestId
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_REQUEST_ID)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public UUID getRequestId() {
    return requestId;
  }


  @JsonProperty(JSON_PROPERTY_REQUEST_ID)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setRequestId(UUID requestId) {
    this.requestId = requestId;
  }


  public IDAidiyatBilgisiGuncellemeResponse response(MakosApiResponse response) {
    
    this.response = response;
    return this;
  }

   /**
   * Get response
   * @return response
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_RESPONSE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public MakosApiResponse getResponse() {
    return response;
  }


  @JsonProperty(JSON_PROPERTY_RESPONSE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setResponse(MakosApiResponse response) {
    this.response = response;
  }


  public IDAidiyatBilgisiGuncellemeResponse evrakId(Long evrakId) {
    
    this.evrakId = evrakId;
    return this;
  }

   /**
   * Get evrakId
   * @return evrakId
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_EVRAK_ID)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Long getEvrakId() {
    return evrakId;
  }


  @JsonProperty(JSON_PROPERTY_EVRAK_ID)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setEvrakId(Long evrakId) {
    this.evrakId = evrakId;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    IDAidiyatBilgisiGuncellemeResponse idAidiyatBilgisiGuncellemeResponse = (IDAidiyatBilgisiGuncellemeResponse) o;
    return Objects.equals(this.requestId, idAidiyatBilgisiGuncellemeResponse.requestId) &&
        Objects.equals(this.response, idAidiyatBilgisiGuncellemeResponse.response) &&
        Objects.equals(this.evrakId, idAidiyatBilgisiGuncellemeResponse.evrakId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(requestId, response, evrakId);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class IDAidiyatBilgisiGuncellemeResponse {\n");
    sb.append("    requestId: ").append(toIndentedString(requestId)).append("\n");
    sb.append("    response: ").append(toIndentedString(response)).append("\n");
    sb.append("    evrakId: ").append(toIndentedString(evrakId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

