package iym.backend.kullanici.mapper;

import org.mapstruct.*;
import iym.backend.kullanici.dto.KullaniciDto;
import iym.backend.kullanici.entity.Kullanici;
import iym.backend.kullanicikullanicigrup.entity.KullaniciKullaniciGrup;
import iym.backend.kullanicigrup.entity.KullaniciGrup;
import iym.backend.kullanicigrupyetki.entity.KullaniciGrupYetki;
import iym.backend.shared.mapper.BaseMapper;

import java.util.List;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring")
public abstract class KullaniciMapper implements BaseMapper<Kullanici, KullaniciDto> {

    @Mapping(target = "kullaniciGrupIdList", expression = "java(" +
            "kullanici.getKullaniciKullaniciGruplar().stream()" +
            ".map(kug -> kug.getKullaniciGrup().getId())" +
            ".collect(java.util.stream.Collectors.toList())" +
            ")")
    @Mapping(target = "kullaniciYetkiIdList", expression = "java(" +
            "kullanici.getKullaniciKullaniciGruplar().stream()" +
            ".flatMap(kug -> kug.getKullaniciGrup().getKullaniciGrupYetkiler().stream())" +
            ".map(kgYetki -> kgYetki.getYetki().getId())" +
            ".distinct().collect(java.util.stream.Collectors.toList())" +
            ")")
    @Mapping(target = "status", expression = "java(kullanici.getStatus().name())")
    public abstract KullaniciDto toDto(Kullanici kullanici);

    @Mapping(target = "kullaniciKullaniciGruplar", ignore = true) // Manuel handle edeceğiz
    public abstract Kullanici toEntity(KullaniciDto dto);

    @AfterMapping
    public  void mapKullaniciGruplar(KullaniciDto dto, @MappingTarget Kullanici entity, @Context List<KullaniciGrup> gruplar) {
        if (dto.getKullaniciGrupIdList() == null) return;

        List<KullaniciKullaniciGrup> iliskiler = dto.getKullaniciGrupIdList().stream()
                .map(id -> {
                    KullaniciGrup grup = gruplar.stream()
                            .filter(g -> g.getId().equals(id))
                            .findFirst()
                            .orElseThrow(() -> new RuntimeException("Grup bulunamadı: " + id));

                    KullaniciKullaniciGrup kkg = new KullaniciKullaniciGrup();
                    kkg.setKullanici(entity);
                    kkg.setKullaniciGrup(grup);
                    return kkg;
                })
                .collect(Collectors.toList());

        entity.setKullaniciKullaniciGruplar(iliskiler);
    }
}

