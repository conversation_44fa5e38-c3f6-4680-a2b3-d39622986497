package iym.backend.kullanicikullanicigrup.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;
import iym.backend.kullanici.entity.Kullanici;
import iym.backend.kullanicigrup.entity.KullaniciGrup;
import iym.backend.shared.entity.BaseEntity;

@Entity
@Table(name = "kullanici_kullanici_gruplar")
@Data
@NoArgsConstructor
@AllArgsConstructor
@SQLDelete(sql = "UPDATE kullanici_kullanici_gruplar SET is_deleted = true WHERE id = ?")
@Where(clause = "is_deleted = false")
public class KullaniciKullaniciGrup extends BaseEntity {

    @ManyToOne
    @JoinColumn(name = "kullanici_id", nullable = false)
    private <PERSON><PERSON><PERSON> kullanici;

    @ManyToOne
    @JoinColumn(name = "kullanici_grup_id", nullable = false)
    private KullaniciGrup kullaniciGrup;
}

