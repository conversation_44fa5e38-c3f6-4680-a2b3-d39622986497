/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * HedefGuncellemeBilgi
 */
@JsonPropertyOrder({
  HedefGuncellemeBilgi.JSON_PROPERTY_HEDEF_GUNCELLEME_ALAN,
  HedefGuncellemeBilgi.JSON_PROPERTY_YENI_DEGERI
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class HedefGuncellemeBilgi {
  /**
   * Gets or Sets hedefGuncellemeAlan
   */
  public enum HedefGuncellemeAlanEnum {
    AD("AD"),
    
    SOYAD("SOYAD"),
    
    HEDEF("HEDEF"),
    
    TCKIMLIKNO("TCKIMlIKNO"),
    
    CANAK_NO("CANAK_NO");

    private String value;

    HedefGuncellemeAlanEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static HedefGuncellemeAlanEnum fromValue(String value) {
      for (HedefGuncellemeAlanEnum b : HedefGuncellemeAlanEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_HEDEF_GUNCELLEME_ALAN = "hedefGuncellemeAlan";
  private HedefGuncellemeAlanEnum hedefGuncellemeAlan;

  public static final String JSON_PROPERTY_YENI_DEGERI = "yeniDegeri";
  private String yeniDegeri;

  public HedefGuncellemeBilgi() {
  }

  public HedefGuncellemeBilgi hedefGuncellemeAlan(HedefGuncellemeAlanEnum hedefGuncellemeAlan) {
    
    this.hedefGuncellemeAlan = hedefGuncellemeAlan;
    return this;
  }

   /**
   * Get hedefGuncellemeAlan
   * @return hedefGuncellemeAlan
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_HEDEF_GUNCELLEME_ALAN)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public HedefGuncellemeAlanEnum getHedefGuncellemeAlan() {
    return hedefGuncellemeAlan;
  }


  @JsonProperty(JSON_PROPERTY_HEDEF_GUNCELLEME_ALAN)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setHedefGuncellemeAlan(HedefGuncellemeAlanEnum hedefGuncellemeAlan) {
    this.hedefGuncellemeAlan = hedefGuncellemeAlan;
  }


  public HedefGuncellemeBilgi yeniDegeri(String yeniDegeri) {
    
    this.yeniDegeri = yeniDegeri;
    return this;
  }

   /**
   * Get yeniDegeri
   * @return yeniDegeri
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_YENI_DEGERI)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getYeniDegeri() {
    return yeniDegeri;
  }


  @JsonProperty(JSON_PROPERTY_YENI_DEGERI)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setYeniDegeri(String yeniDegeri) {
    this.yeniDegeri = yeniDegeri;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    HedefGuncellemeBilgi hedefGuncellemeBilgi = (HedefGuncellemeBilgi) o;
    return Objects.equals(this.hedefGuncellemeAlan, hedefGuncellemeBilgi.hedefGuncellemeAlan) &&
        Objects.equals(this.yeniDegeri, hedefGuncellemeBilgi.yeniDegeri);
  }

  @Override
  public int hashCode() {
    return Objects.hash(hedefGuncellemeAlan, yeniDegeri);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class HedefGuncellemeBilgi {\n");
    sb.append("    hedefGuncellemeAlan: ").append(toIndentedString(hedefGuncellemeAlan)).append("\n");
    sb.append("    yeniDegeri: ").append(toIndentedString(yeniDegeri)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

