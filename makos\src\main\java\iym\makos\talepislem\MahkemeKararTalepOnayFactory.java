package iym.makos.talepislem;


import iym.common.model.api.KararTuru;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class MahkemeKararTalepOnayFactory {

    private final Map<KararTuru, MahkemeKararTalepIsleyici> isleyiciListByKararTuru = new HashMap<>();

    @Autowired
    public MahkemeKararTalepOnayFactory(List<MahkemeKararTalepIsleyici> talepIsleyiciler) {
        for (MahkemeKararTalepIsleyici validator : talepIsleyiciler) {
            isleyiciListByKararTuru.put(validator.getRelatedKararTuru(), validator);
        }
    }

    public MahkemeKararTalepIsleyici getKararTalepIsleyici(KararTuru kararTuru) {
        return isleyiciListByKararTuru.get(kararTuru);
    }

}