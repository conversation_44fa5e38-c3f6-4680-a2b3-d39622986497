package iym.makos.handler;

import iym.common.model.entity.iym.HtsHedeflerTalep;
import iym.db.jpa.dao.HtsHedeflerTalepRepo;
import iym.makos.dto.it.ITKararRequest;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.ITHedefDetay;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
@Slf4j
public class ITKararDBSaveHandler extends MahkemeKararRequestDbSaveHandlerBase<ITKararRequest> {

    private KararRequestMapper kararRequestMapper;
    private HtsHedeflerTalepRepo htsHedeflerTalepRepo;

    @Autowired
    public ITKararDBSaveHandler(HtsHedeflerTalepRepo htsHedeflerTalepRepo, KararRequestMapper kararRequestMapper) {
        this.htsHedeflerTalepRepo = htsHedeflerTalepRepo;
        this.kararRequestMapper = kararRequestMapper;
    }

    @Override
    public Long kaydet(ITKararRequest request, Date kayitTarihi, Long kullaniciId) throws Exception {
        try {
            Long mahkemeKararTalepId = mahkemeKararRequestCommonDbSaver.handleDbSave(request, kayitTarihi, kullaniciId);

            //buradaki mahkeme karar talep id  HTS_MAHKEME_KARAR_TALEP tablosunun id'sidir.
            for (ITHedefDetay ITHedefDetay : request.getHedefDetayListesi()) {
                HtsHedeflerTalep htsHedeflerTalep = new HtsHedeflerTalep();
                htsHedeflerTalep.setMahkemeKararId(mahkemeKararTalepId);
                htsHedeflerTalepRepo.save(htsHedeflerTalep);
            }

            return mahkemeKararTalepId;
        } catch (Exception ex) {
            log.error("ITKarar handleDbSave failed. id:{}, evrakNo:{}", request.getId(), request.getEvrakDetay().getEvrakNo(), ex);
            throw new RuntimeException(ex);
        }
    }

}

