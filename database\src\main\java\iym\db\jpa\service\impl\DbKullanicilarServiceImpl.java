package iym.db.jpa.service.impl;


import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.Kullanicilar;
import iym.common.service.db.DbKullanicilarService;
import iym.db.jpa.dao.KullanicilarRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;


@Service
public class DbKullanicilarServiceImpl extends GenericDbServiceImpl<Kullanicilar, Long> implements DbKullanicilarService {

    @Autowired
    public DbKullanicilarServiceImpl(KullanicilarRepo repository) {
        super(repository);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Kullanicilar> findByKullaniciAdi(String kullaniciAdi) {
        return getRepository().findByKullaniciAdi(kullaniciAdi);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Kullanicilar> findByDurumu(String durumu) {
        return getRepository().findByDurumu(durumu);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Kullanicilar> findAllByOrderByKullaniciAdiAsc() {
        return getRepository().findAllByOrderByKullaniciAdiAsc();
    }

    public KullanicilarRepo getRepository() {
        return (KullanicilarRepo) repository;
    }

}
