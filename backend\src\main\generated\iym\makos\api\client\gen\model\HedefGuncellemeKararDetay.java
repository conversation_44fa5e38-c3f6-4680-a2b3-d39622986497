/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import iym.makos.api.client.gen.model.HedefGuncellemeDetay;
import iym.makos.api.client.gen.model.MahkemeKararDetay;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * <PERSON><PERSON>ncelleme yapılacak hedefler için mahkeme karar bilgisi ve karara ait güncellenecek ad, soyad bilgileri
 */
@JsonPropertyOrder({
  HedefGuncellemeKararDetay.JSON_PROPERTY_MAHKEME_KARAR_DETAY,
  HedefGuncellemeKararDetay.JSON_PROPERTY_HEDEF_GUNCELLEME_DETAY_LISTESI
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class HedefGuncellemeKararDetay {
  public static final String JSON_PROPERTY_MAHKEME_KARAR_DETAY = "mahkemeKararDetay";
  private MahkemeKararDetay mahkemeKararDetay;

  public static final String JSON_PROPERTY_HEDEF_GUNCELLEME_DETAY_LISTESI = "hedefGuncellemeDetayListesi";
  private List<HedefGuncellemeDetay> hedefGuncellemeDetayListesi = new ArrayList<>();

  public HedefGuncellemeKararDetay() {
  }

  public HedefGuncellemeKararDetay mahkemeKararDetay(MahkemeKararDetay mahkemeKararDetay) {
    
    this.mahkemeKararDetay = mahkemeKararDetay;
    return this;
  }

   /**
   * Get mahkemeKararDetay
   * @return mahkemeKararDetay
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DETAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public MahkemeKararDetay getMahkemeKararDetay() {
    return mahkemeKararDetay;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DETAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMahkemeKararDetay(MahkemeKararDetay mahkemeKararDetay) {
    this.mahkemeKararDetay = mahkemeKararDetay;
  }


  public HedefGuncellemeKararDetay hedefGuncellemeDetayListesi(List<HedefGuncellemeDetay> hedefGuncellemeDetayListesi) {
    
    this.hedefGuncellemeDetayListesi = hedefGuncellemeDetayListesi;
    return this;
  }

  public HedefGuncellemeKararDetay addHedefGuncellemeDetayListesiItem(HedefGuncellemeDetay hedefGuncellemeDetayListesiItem) {
    if (this.hedefGuncellemeDetayListesi == null) {
      this.hedefGuncellemeDetayListesi = new ArrayList<>();
    }
    this.hedefGuncellemeDetayListesi.add(hedefGuncellemeDetayListesiItem);
    return this;
  }

   /**
   * Get hedefGuncellemeDetayListesi
   * @return hedefGuncellemeDetayListesi
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_HEDEF_GUNCELLEME_DETAY_LISTESI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public List<HedefGuncellemeDetay> getHedefGuncellemeDetayListesi() {
    return hedefGuncellemeDetayListesi;
  }


  @JsonProperty(JSON_PROPERTY_HEDEF_GUNCELLEME_DETAY_LISTESI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setHedefGuncellemeDetayListesi(List<HedefGuncellemeDetay> hedefGuncellemeDetayListesi) {
    this.hedefGuncellemeDetayListesi = hedefGuncellemeDetayListesi;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    HedefGuncellemeKararDetay hedefGuncellemeKararDetay = (HedefGuncellemeKararDetay) o;
    return Objects.equals(this.mahkemeKararDetay, hedefGuncellemeKararDetay.mahkemeKararDetay) &&
        Objects.equals(this.hedefGuncellemeDetayListesi, hedefGuncellemeKararDetay.hedefGuncellemeDetayListesi);
  }

  @Override
  public int hashCode() {
    return Objects.hash(mahkemeKararDetay, hedefGuncellemeDetayListesi);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class HedefGuncellemeKararDetay {\n");
    sb.append("    mahkemeKararDetay: ").append(toIndentedString(mahkemeKararDetay)).append("\n");
    sb.append("    hedefGuncellemeDetayListesi: ").append(toIndentedString(hedefGuncellemeDetayListesi)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

