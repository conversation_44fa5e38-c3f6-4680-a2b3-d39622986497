package iym.makos.dto.talepupdate;

import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.UUID;

@Data
@Builder
@ToString
@EqualsAndHashCode
public class MahkemeKararTalepUpdateResponse {

    @NotNull
    private UUID requestId;

    @NotNull
    @Valid
    private MakosApiResponse response;

    public boolean isSuccess(){
        return response.getResponseCode() == MakosResponseCode.SUCCESS;
    }

}