-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for MAHKEME_SUCLAR if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'MAHKEME_SUCLAR_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.MAHKEME_SUCLAR_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create MAHKEME_SUCLAR table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'MAHKEME_SUCLAR';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.MAHKEME_SUCLAR (
      ID NUMBER NOT NULL,
      MAHKEME_KARAR_ID NUMBER NOT NULL,
      MAHKEME_SUC_TIP_KOD VARCHAR2(10 BYTE) NOT NULL,
      DURUMU VARCHAR2(20 BYTE)
    )';
    
    -- Create unique index
    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX iym.MAH_SUCLAR_IDX ON iym.MAHKEME_SUCLAR (MAHKEME_KARAR_ID ASC, MAHKEME_SUC_TIP_KOD ASC)';
  END IF;
END;
/


COMMIT;
