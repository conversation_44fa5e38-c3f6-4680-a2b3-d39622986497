/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import iym.makos.api.client.gen.model.MakosUserAuditLog;
import iym.makos.api.client.gen.model.ModelApiResponse;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * GetMakosUserAuditLogResponse
 */
@JsonPropertyOrder({
  GetMakosUserAuditLogResponse.JSON_PROPERTY_RESPONSE,
  GetMakosUserAuditLogResponse.JSON_PROPERTY_AUDIT_LOG
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class GetMakosUserAuditLogResponse {
  public static final String JSON_PROPERTY_RESPONSE = "response";
  private ModelApiResponse response;

  public static final String JSON_PROPERTY_AUDIT_LOG = "auditLog";
  private MakosUserAuditLog auditLog;

  public GetMakosUserAuditLogResponse() {
  }

  public GetMakosUserAuditLogResponse response(ModelApiResponse response) {
    
    this.response = response;
    return this;
  }

   /**
   * Get response
   * @return response
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_RESPONSE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public ModelApiResponse getResponse() {
    return response;
  }


  @JsonProperty(JSON_PROPERTY_RESPONSE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setResponse(ModelApiResponse response) {
    this.response = response;
  }


  public GetMakosUserAuditLogResponse auditLog(MakosUserAuditLog auditLog) {
    
    this.auditLog = auditLog;
    return this;
  }

   /**
   * Get auditLog
   * @return auditLog
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_AUDIT_LOG)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public MakosUserAuditLog getAuditLog() {
    return auditLog;
  }


  @JsonProperty(JSON_PROPERTY_AUDIT_LOG)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAuditLog(MakosUserAuditLog auditLog) {
    this.auditLog = auditLog;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    GetMakosUserAuditLogResponse getMakosUserAuditLogResponse = (GetMakosUserAuditLogResponse) o;
    return Objects.equals(this.response, getMakosUserAuditLogResponse.response) &&
        Objects.equals(this.auditLog, getMakosUserAuditLogResponse.auditLog);
  }

  @Override
  public int hashCode() {
    return Objects.hash(response, auditLog);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class GetMakosUserAuditLogResponse {\n");
    sb.append("    response: ").append(toIndentedString(response)).append("\n");
    sb.append("    auditLog: ").append(toIndentedString(auditLog)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

