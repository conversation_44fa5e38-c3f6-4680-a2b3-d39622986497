package iym.backend.postgresql.shared.security;

import lombok.RequiredArgsConstructor;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import iym.backend.postgresql.kullanici.entity.Kullanici;
import iym.backend.postgresql.kullanici.repository.KullaniciRepository;

import java.util.List;

@Service
@RequiredArgsConstructor
public class KullaniciDetailsService implements UserDetailsService {

    private final KullaniciRepository kullaniciRepository;

    @Override
    @Transactional(readOnly = true, transactionManager = "postgresqlTransactionManager")
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        Kullanici kullanici = kullaniciRepository.findByKullaniciAdi(username)
                .orElseThrow(() -> new UsernameNotFoundException("Kullanıcı bulunamadı"));

        List<SimpleGrantedAuthority> authorities = kullanici.getKullaniciKullaniciGruplar().stream()
                .flatMap(grup -> grup.getKullaniciGrup().getKullaniciGrupYetkiler().stream())
                .map(kgy -> new SimpleGrantedAuthority(kgy.getYetki().getAd()))
                .toList();

        return new org.springframework.security.core.userdetails.User(
                kullanici.getKullaniciAdi(),
                kullanici.getParola(),
                authorities
        );
    }
}
