package iym.backend.makosclient.config;

import iym.makos.api.client.gen.api.HealthCheckControllerApi;
import iym.makos.api.client.gen.api.MahkemeKararControllerApi;
import iym.makos.api.client.gen.api.UserControllerApi;
import iym.makos.api.client.gen.api.AuthControllerApi;
import iym.makos.api.client.gen.api.AuditControllerApi;
import iym.makos.api.client.gen.handler.ApiClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MAKOS Client Configuration
 * Generated MAKOS API client'ları için configuration
 */
@Configuration
@Slf4j
public class MakosClientConfig {

    @Value("${makos.api.base-url:http://localhost:8080}")
    private String makosBaseUrl;

    @Value("${makos.api.timeout:30000}")
    private int timeout;

    @Bean
    public ApiClient makosApiClient() {
        ApiClient apiClient = new ApiClient();
        apiClient.setBasePath(makosBaseUrl);
        apiClient.setConnectTimeout(timeout);
        apiClient.setReadTimeout(timeout);
        log.info("MAKOS API Client configured with base URL: {}", makosBaseUrl);
        return apiClient;
    }

    @Bean
    public HealthCheckControllerApi healthCheckControllerApi(ApiClient apiClient) {
        return new HealthCheckControllerApi(apiClient);
    }

    @Bean
    public MahkemeKararControllerApi mahkemeKararControllerApi(ApiClient apiClient) {
        return new MahkemeKararControllerApi(apiClient);
    }

    @Bean
    public UserControllerApi userControllerApi(ApiClient apiClient) {
        return new UserControllerApi(apiClient);
    }

    @Bean
    public AuthControllerApi authControllerApi(ApiClient apiClient) {
        return new AuthControllerApi(apiClient);
    }

    @Bean
    public AuditControllerApi auditControllerApi(ApiClient apiClient) {
        return new AuditControllerApi(apiClient);
    }
}
