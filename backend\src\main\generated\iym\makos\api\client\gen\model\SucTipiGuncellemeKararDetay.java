/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import iym.makos.api.client.gen.model.MahkemeKararDetay;
import iym.makos.api.client.gen.model.SucTipiGuncellemeDetay;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * G<PERSON>ncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek aidiyat bilgileri
 */
@JsonPropertyOrder({
  SucTipiGuncellemeKararDetay.JSON_PROPERTY_MAHKEME_KARAR_DETAY,
  SucTipiGuncellemeKararDetay.JSON_PROPERTY_SUC_TIPI_GUNCELLEME_DETAY_LISTESI
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class SucTipiGuncellemeKararDetay {
  public static final String JSON_PROPERTY_MAHKEME_KARAR_DETAY = "mahkemeKararDetay";
  private MahkemeKararDetay mahkemeKararDetay;

  public static final String JSON_PROPERTY_SUC_TIPI_GUNCELLEME_DETAY_LISTESI = "sucTipiGuncellemeDetayListesi";
  private List<SucTipiGuncellemeDetay> sucTipiGuncellemeDetayListesi = new ArrayList<>();

  public SucTipiGuncellemeKararDetay() {
  }

  public SucTipiGuncellemeKararDetay mahkemeKararDetay(MahkemeKararDetay mahkemeKararDetay) {
    
    this.mahkemeKararDetay = mahkemeKararDetay;
    return this;
  }

   /**
   * Get mahkemeKararDetay
   * @return mahkemeKararDetay
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DETAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public MahkemeKararDetay getMahkemeKararDetay() {
    return mahkemeKararDetay;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DETAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMahkemeKararDetay(MahkemeKararDetay mahkemeKararDetay) {
    this.mahkemeKararDetay = mahkemeKararDetay;
  }


  public SucTipiGuncellemeKararDetay sucTipiGuncellemeDetayListesi(List<SucTipiGuncellemeDetay> sucTipiGuncellemeDetayListesi) {
    
    this.sucTipiGuncellemeDetayListesi = sucTipiGuncellemeDetayListesi;
    return this;
  }

  public SucTipiGuncellemeKararDetay addSucTipiGuncellemeDetayListesiItem(SucTipiGuncellemeDetay sucTipiGuncellemeDetayListesiItem) {
    if (this.sucTipiGuncellemeDetayListesi == null) {
      this.sucTipiGuncellemeDetayListesi = new ArrayList<>();
    }
    this.sucTipiGuncellemeDetayListesi.add(sucTipiGuncellemeDetayListesiItem);
    return this;
  }

   /**
   * Get sucTipiGuncellemeDetayListesi
   * @return sucTipiGuncellemeDetayListesi
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_SUC_TIPI_GUNCELLEME_DETAY_LISTESI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public List<SucTipiGuncellemeDetay> getSucTipiGuncellemeDetayListesi() {
    return sucTipiGuncellemeDetayListesi;
  }


  @JsonProperty(JSON_PROPERTY_SUC_TIPI_GUNCELLEME_DETAY_LISTESI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setSucTipiGuncellemeDetayListesi(List<SucTipiGuncellemeDetay> sucTipiGuncellemeDetayListesi) {
    this.sucTipiGuncellemeDetayListesi = sucTipiGuncellemeDetayListesi;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SucTipiGuncellemeKararDetay sucTipiGuncellemeKararDetay = (SucTipiGuncellemeKararDetay) o;
    return Objects.equals(this.mahkemeKararDetay, sucTipiGuncellemeKararDetay.mahkemeKararDetay) &&
        Objects.equals(this.sucTipiGuncellemeDetayListesi, sucTipiGuncellemeKararDetay.sucTipiGuncellemeDetayListesi);
  }

  @Override
  public int hashCode() {
    return Objects.hash(mahkemeKararDetay, sucTipiGuncellemeDetayListesi);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SucTipiGuncellemeKararDetay {\n");
    sb.append("    mahkemeKararDetay: ").append(toIndentedString(mahkemeKararDetay)).append("\n");
    sb.append("    sucTipiGuncellemeDetayListesi: ").append(toIndentedString(sucTipiGuncellemeDetayListesi)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

