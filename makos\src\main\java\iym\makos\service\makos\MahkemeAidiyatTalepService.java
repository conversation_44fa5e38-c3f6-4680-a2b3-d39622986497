package iym.makos.service.makos;

import iym.common.model.entity.iym.MahkemeAidiyatTalep;
import iym.common.service.db.DbMahkemeAidiyatTalepService;
import iym.makos.dto.MahkemeAidiyatTalepDTO;
import iym.makos.mapper.MahkemeAidiyatTalepMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.Optional;

/**
 * Service for MahkemeAidiyatTalep operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MahkemeAidiyatTalepService {

    private final DbMahkemeAidiyatTalepService dbMahkemeAidiyatTalepService;
    private final MahkemeAidiyatTalepMapper mahkemeAidiyatTalepMapper;

    /**
     * Get all mahkeme aidiyat talep records
     * @return List of MahkemeAidiyatTalepDTO
     */
    public List<MahkemeAidiyatTalepDTO> findAll() {
        List<MahkemeAidiyatTalep> mahkemeAidiyatTalepList = dbMahkemeAidiyatTalepService.findAll();
        return mahkemeAidiyatTalepMapper.toDtoList(mahkemeAidiyatTalepList);
    }

    /**
     * Get mahkeme aidiyat talep by ID
     * @param id Mahkeme aidiyat talep ID
     * @return MahkemeAidiyatTalepDTO
     * @throws ResponseStatusException if not found
     */
    public MahkemeAidiyatTalepDTO findById(Long id) {
        return dbMahkemeAidiyatTalepService.findById(id)
                .map(mahkemeAidiyatTalepMapper::toDto)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme aidiyat talep bulunamadı: " + id));
    }

    /**
     * Get mahkeme aidiyat talep records by mahkeme ID
     * @param mahkemeId Mahkeme ID
     * @return List of MahkemeAidiyatTalepDTO
     */
    public List<MahkemeAidiyatTalepDTO> findByMahkemeId(Long mahkemeId) {
        List<MahkemeAidiyatTalep> mahkemeAidiyatTalepList = dbMahkemeAidiyatTalepService.findByMahkemeKararTalepId(mahkemeId);
        return mahkemeAidiyatTalepMapper.toDtoList(mahkemeAidiyatTalepList);
    }




    /**
     * Get paginated mahkeme aidiyat talep records
     * @param pageable Pageable
     * @return Page of MahkemeAidiyatTalepDTO
     */
    public Page<MahkemeAidiyatTalepDTO> findAll(Pageable pageable) {
        Page<MahkemeAidiyatTalep> mahkemeAidiyatTalepPage = dbMahkemeAidiyatTalepService.findAll(pageable);
        List<MahkemeAidiyatTalepDTO> dtoList = mahkemeAidiyatTalepMapper.toDtoList(mahkemeAidiyatTalepPage.getContent());
        return new PageImpl<>(dtoList, pageable, mahkemeAidiyatTalepPage.getTotalElements());
    }

    /**
     * Create new mahkeme aidiyat talep
     * @param mahkemeAidiyatTalepDTO MahkemeAidiyatTalepDTO
     * @return Created MahkemeAidiyatTalepDTO
     */
    public MahkemeAidiyatTalepDTO create(MahkemeAidiyatTalepDTO mahkemeAidiyatTalepDTO) {


        /*
        // Check if mahkeme aidiyat already exists
        if (mahkemeAidiyatTalepDTO.getMahkemeKararTalepId() != null && mahkemeAidiyatTalepDTO.getAidiyatKod() != null) {
            Optional<MahkemeAidiyatTalep> existingMahkemeAidiyatTalep =
                dbMahkemeAidiyatTalepService(
                    mahkemeAidiyatTalepDTO.getMahkemeKararTalepId(),
                    mahkemeAidiyatTalepDTO.getAidiyatKod());
                    
            if (existingMahkemeAidiyatTalep.isPresent()) {
                throw new ResponseStatusException(HttpStatus.CONFLICT, 
                    "Bu mahkeme aidiyat talebi zaten mevcut: Mahkeme ID: " + 
                    mahkemeAidiyatTalepDTO.getMahkemeKararTalepId() + ", Aidiyat Kod: " +
                    mahkemeAidiyatTalepDTO.getAidiyatKod());
            }
        }
        */
        MahkemeAidiyatTalep mahkemeAidiyatTalep = mahkemeAidiyatTalepMapper.toEntity(mahkemeAidiyatTalepDTO);
        dbMahkemeAidiyatTalepService.save(mahkemeAidiyatTalep);
        log.info("Mahkeme aidiyat talep oluşturuldu: {}", mahkemeAidiyatTalep.getId());
        return mahkemeAidiyatTalepMapper.toDto(mahkemeAidiyatTalep);
    }

    /**
     * Update mahkeme aidiyat talep
     * @param id Mahkeme aidiyat talep ID
     * @param mahkemeAidiyatTalepDTO MahkemeAidiyatTalepDTO
     * @return Updated MahkemeAidiyatTalepDTO
     */
    public MahkemeAidiyatTalepDTO update(Long id, MahkemeAidiyatTalepDTO mahkemeAidiyatTalepDTO) {
        MahkemeAidiyatTalep existingMahkemeAidiyatTalep = dbMahkemeAidiyatTalepService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme aidiyat talep bulunamadı: " + id));

        // Check if mahkeme aidiyat is being changed and already exists
        if (mahkemeAidiyatTalepDTO.getMahkemeKararTalepId() != null &&
            mahkemeAidiyatTalepDTO.getAidiyatKod() != null &&
            (!mahkemeAidiyatTalepDTO.getMahkemeKararTalepId().equals(existingMahkemeAidiyatTalep.getMahkemeKararTalepId()) ||
             !mahkemeAidiyatTalepDTO.getAidiyatKod().equals(existingMahkemeAidiyatTalep.getAidiyatKod()))) {
            
            Optional<MahkemeAidiyatTalep> mahkemeAidiyatTalepWithSameDetails = 
                dbMahkemeAidiyatTalepService.findByMahkemeKararTalepIdAndAidiyatKod(
                    mahkemeAidiyatTalepDTO.getMahkemeKararTalepId(),
                    mahkemeAidiyatTalepDTO.getAidiyatKod());
                    
            if (mahkemeAidiyatTalepWithSameDetails.isPresent() && 
                !mahkemeAidiyatTalepWithSameDetails.get().getId().equals(id)) {
                throw new ResponseStatusException(HttpStatus.CONFLICT, 
                    "Bu mahkeme aidiyat talebi zaten mevcut: Mahkeme ID: " + 
                    mahkemeAidiyatTalepDTO.getAidiyatKod() + ", Aidiyat Kod: " +
                    mahkemeAidiyatTalepDTO.getAidiyatKod());
            }
        }

        MahkemeAidiyatTalep updatedMahkemeAidiyatTalep = mahkemeAidiyatTalepMapper.updateEntityFromDto(existingMahkemeAidiyatTalep, mahkemeAidiyatTalepDTO);
        dbMahkemeAidiyatTalepService.update(updatedMahkemeAidiyatTalep);
        log.info("Mahkeme aidiyat talep güncellendi: {}", updatedMahkemeAidiyatTalep.getId());
        return mahkemeAidiyatTalepMapper.toDto(updatedMahkemeAidiyatTalep);
    }

    /**
     * Delete mahkeme aidiyat talep
     * @param id Mahkeme aidiyat talep ID
     */
    public void delete(Long id) {
        MahkemeAidiyatTalep mahkemeAidiyatTalep = dbMahkemeAidiyatTalepService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme aidiyat talep bulunamadı: " + id));
        
        dbMahkemeAidiyatTalepService.delete(mahkemeAidiyatTalep);
        log.info("Mahkeme aidiyat talep silindi: {}", id);
    }
}
