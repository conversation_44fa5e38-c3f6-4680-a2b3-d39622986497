/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import iym.makos.api.client.gen.model.MahkemeKararDetay;
import iym.makos.api.client.gen.model.MahkemeKararGuncellemeBilgi;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek yeni kod/il bilgileri
 */
@JsonPropertyOrder({
  MahkemeKararGuncellemeDetay.JSON_PROPERTY_MAHKEME_KARAR_DETAY,
  MahkemeKararGuncellemeDetay.JSON_PROPERTY_MAHKEME_KARAR_GUNCELLEME_BILGI_LISTESI
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class MahkemeKararGuncellemeDetay {
  public static final String JSON_PROPERTY_MAHKEME_KARAR_DETAY = "mahkemeKararDetay";
  private MahkemeKararDetay mahkemeKararDetay;

  public static final String JSON_PROPERTY_MAHKEME_KARAR_GUNCELLEME_BILGI_LISTESI = "mahkemeKararGuncellemeBilgiListesi";
  private List<MahkemeKararGuncellemeBilgi> mahkemeKararGuncellemeBilgiListesi = new ArrayList<>();

  public MahkemeKararGuncellemeDetay() {
  }

  public MahkemeKararGuncellemeDetay mahkemeKararDetay(MahkemeKararDetay mahkemeKararDetay) {
    
    this.mahkemeKararDetay = mahkemeKararDetay;
    return this;
  }

   /**
   * Get mahkemeKararDetay
   * @return mahkemeKararDetay
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DETAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public MahkemeKararDetay getMahkemeKararDetay() {
    return mahkemeKararDetay;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DETAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMahkemeKararDetay(MahkemeKararDetay mahkemeKararDetay) {
    this.mahkemeKararDetay = mahkemeKararDetay;
  }


  public MahkemeKararGuncellemeDetay mahkemeKararGuncellemeBilgiListesi(List<MahkemeKararGuncellemeBilgi> mahkemeKararGuncellemeBilgiListesi) {
    
    this.mahkemeKararGuncellemeBilgiListesi = mahkemeKararGuncellemeBilgiListesi;
    return this;
  }

  public MahkemeKararGuncellemeDetay addMahkemeKararGuncellemeBilgiListesiItem(MahkemeKararGuncellemeBilgi mahkemeKararGuncellemeBilgiListesiItem) {
    if (this.mahkemeKararGuncellemeBilgiListesi == null) {
      this.mahkemeKararGuncellemeBilgiListesi = new ArrayList<>();
    }
    this.mahkemeKararGuncellemeBilgiListesi.add(mahkemeKararGuncellemeBilgiListesiItem);
    return this;
  }

   /**
   * Get mahkemeKararGuncellemeBilgiListesi
   * @return mahkemeKararGuncellemeBilgiListesi
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_GUNCELLEME_BILGI_LISTESI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public List<MahkemeKararGuncellemeBilgi> getMahkemeKararGuncellemeBilgiListesi() {
    return mahkemeKararGuncellemeBilgiListesi;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_GUNCELLEME_BILGI_LISTESI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMahkemeKararGuncellemeBilgiListesi(List<MahkemeKararGuncellemeBilgi> mahkemeKararGuncellemeBilgiListesi) {
    this.mahkemeKararGuncellemeBilgiListesi = mahkemeKararGuncellemeBilgiListesi;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MahkemeKararGuncellemeDetay mahkemeKararGuncellemeDetay = (MahkemeKararGuncellemeDetay) o;
    return Objects.equals(this.mahkemeKararDetay, mahkemeKararGuncellemeDetay.mahkemeKararDetay) &&
        Objects.equals(this.mahkemeKararGuncellemeBilgiListesi, mahkemeKararGuncellemeDetay.mahkemeKararGuncellemeBilgiListesi);
  }

  @Override
  public int hashCode() {
    return Objects.hash(mahkemeKararDetay, mahkemeKararGuncellemeBilgiListesi);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MahkemeKararGuncellemeDetay {\n");
    sb.append("    mahkemeKararDetay: ").append(toIndentedString(mahkemeKararDetay)).append("\n");
    sb.append("    mahkemeKararGuncellemeBilgiListesi: ").append(toIndentedString(mahkemeKararGuncellemeBilgiListesi)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

