package iym.backend.config;

import iym.backend.config.properties.OracleJpaProperties;
import iym.backend.config.properties.PostgresqlJpaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * JPA Properties configuration for multi-datasource setup
 * <p>
 * This configuration class creates JPA properties beans separately from
 * DataSource configurations to avoid circular dependencies.
 */
@Configuration
public class JpaPropertiesConfig {

    /**
     * Oracle JPA properties bean factory.
     * <p>
     * Creates and configures Oracle-specific JPA settings from application.properties
     * with prefix 'spring.jpa.oracle'.
     *
     * @return configured Oracle JPA properties instance
     */
    @Bean("oracleJpaProperties")
    @ConfigurationProperties(prefix = "spring.jpa.oracle")
    public OracleJpaProperties oracleJpaProperties() {
        return new OracleJpaProperties();
    }

    /**
     * PostgreSQL JPA properties bean factory.
     * <p>
     * Creates and configures PostgreSQL-specific JPA settings from application.properties
     * with prefix 'spring.jpa.postgresql'.
     *
     * @return configured PostgreSQL JPA properties instance
     */
    @Bean("postgresqlJpaProperties")
    @ConfigurationProperties(prefix = "spring.jpa.postgresql")
    public PostgresqlJpaProperties postgresqlJpaProperties() {
        return new PostgresqlJpaProperties();
    }
}
