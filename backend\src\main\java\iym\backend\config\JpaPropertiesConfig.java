package iym.backend.config;

import iym.backend.config.properties.PostgresqlJpaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * JPA Properties configuration for PostgreSQL datasource
 * <p>
 * This configuration class creates JPA properties beans separately from
 * DataSource configurations to avoid circular dependencies.
 */
@Configuration
public class JpaPropertiesConfig {

    /**
     * PostgreSQL JPA properties bean factory.
     * <p>
     * Creates and configures PostgreSQL-specific JPA settings from application.properties
     * with prefix 'spring.jpa.postgresql'.
     *
     * @return configured PostgreSQL JPA properties instance
     */
    @Bean("postgresqlJpaProperties")
    @ConfigurationProperties(prefix = "spring.jpa.postgresql")
    public PostgresqlJpaProperties postgresqlJpaProperties() {
        return new PostgresqlJpaProperties();
    }
}
