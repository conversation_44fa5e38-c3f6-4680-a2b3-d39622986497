package iym.backend.postgresql.menuitemyetki.entity;

import jakarta.persistence.*;
import lombok.*;
import iym.backend.postgresql.menuitem.entity.MenuItem;
import iym.backend.postgresql.shared.entity.BaseEntity;
import iym.backend.postgresql.yetki.entity.Yetki;

@Entity
@Table(name = "menu_item_yetkiler")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MenuItemYetki extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "menu_item_id")
    private MenuItem menuItem;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "yetki_id")
    private Yetki yetki;
}
