package iym.makos.service.makos;

import iym.common.model.entity.iym.EvrakKayit;
import iym.common.service.db.DbEvrakKayitService;
import iym.makos.dto.EvrakKayitDTO;
import iym.makos.mapper.EvrakKayitMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.Date;
import java.util.List;

/**
 * Service for EvrakKayit operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class EvrakKayitService {

    private final DbEvrakKayitService dbEvrakKayitService;
    private final EvrakKayitMapper evrakKayitMapper;

    /**
     * Get all evrak kayit records
     * @return List of EvrakKayitDTO
     */
    public List<EvrakKayitDTO> findAll() {
        List<EvrakKayit> evrakKayitList = dbEvrakKayitService.findAll();
        return evrakKayitMapper.toDtoList(evrakKayitList);
    }

    /**
     * Get paginated evrak kayit records
     * @param pageable Pageable
     * @return Page of EvrakKayitDTO
     */
    public Page<EvrakKayitDTO> findAll(Pageable pageable) {
        Page<EvrakKayit> evrakKayitPage = dbEvrakKayitService.findAll(pageable);
        List<EvrakKayitDTO> dtoList = evrakKayitMapper.toDtoList(evrakKayitPage.getContent());
        return new PageImpl<>(dtoList, pageable, evrakKayitPage.getTotalElements());
    }

    /**
     * Get evrak kayit by ID
     * @param id Evrak kayit ID
     * @return EvrakKayitDTO
     * @throws ResponseStatusException if not found
     */
    public EvrakKayitDTO findById(Long id) {
        return dbEvrakKayitService.findById(id)
                .map(evrakKayitMapper::toDto)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Evrak kayıt bulunamadı: " + id));
    }

    /**
     * Get evrak kayit by evrak sira no
     * @param evrakSiraNo Evrak sira no
     * @return EvrakKayitDTO
     * @throws ResponseStatusException if not found
     */
    public boolean existsByEvrakSiraNo(String evrakSiraNo) {
        return dbEvrakKayitService.existsByEvrakSiraNo(evrakSiraNo);
    }

    /**
     * Find evrak kayit by criteria
     * @param evrakNo Evrak no
     * @param evrakTarihi Evrak tarihi
     * @param gelIl Geldiği il
     * @param evrakGeldigiKurum Evrak geldiği kurum
     * @param durumu Durumu
     * @return EvrakKayitDTO
     * @throws ResponseStatusException if not found
     */

    /**
     * Find evrak kayit by evrak tipi
     * @param evrakTipi Evrak tipi
     * @return List of EvrakKayitDTO
     */
    public List<EvrakKayitDTO> findByEvrakTipi(String evrakTipi) {
        List<EvrakKayit> evrakKayitList = dbEvrakKayitService.findByEvrakTipi(evrakTipi);
        return evrakKayitMapper.toDtoList(evrakKayitList);
    }

    /**
     * Find evrak kayit by giris tarih between
     * @param startDate Start date
     * @param endDate End date
     * @return List of EvrakKayitDTO
     */
    public List<EvrakKayitDTO> findByGirisTarihBetween(Date startDate, Date endDate) {
        List<EvrakKayit> evrakKayitList = dbEvrakKayitService.findByGirisTarihBetween(startDate, endDate);
        return evrakKayitMapper.toDtoList(evrakKayitList);
    }

    /**
     * Find evrak kayit by durumu
     * @param durumu Durumu
     * @return List of EvrakKayitDTO
     */
    public List<EvrakKayitDTO> findByDurumu(String durumu) {
        List<EvrakKayit> evrakKayitList = dbEvrakKayitService.findByDurumu(durumu);
        return evrakKayitMapper.toDtoList(evrakKayitList);
    }

    /**
     * Find evrak kayit by havale birim
     * @param havaleBirim Havale birim
     * @return List of EvrakKayitDTO
     */
    public List<EvrakKayitDTO> findByHavaleBirim(String havaleBirim) {
        List<EvrakKayit> evrakKayitList = dbEvrakKayitService.findByHavaleBirim(havaleBirim);
        return evrakKayitMapper.toDtoList(evrakKayitList);
    }



    /**
     * Find evrak kayit by acilmi
     * @param acilmi Acilmi
     * @return List of EvrakKayitDTO
     */
    public List<EvrakKayitDTO> findByAcilmi(String acilmi) {
        List<EvrakKayit> evrakKayitList = dbEvrakKayitService.findByAcilmi(acilmi);
        return evrakKayitMapper.toDtoList(evrakKayitList);
    }

    /**
     * Create new evrak kayit
     * @param evrakKayitDTO EvrakKayitDTO
     * @return Created EvrakKayitDTO
     */
    public EvrakKayitDTO create(EvrakKayitDTO evrakKayitDTO) {
        // Check if evrakSiraNo already exists
        if (evrakKayitDTO.getEvrakSiraNo() != null) {
            boolean exists = dbEvrakKayitService.existsByEvrakSiraNo(evrakKayitDTO.getEvrakSiraNo());
            if (exists) {
                throw new ResponseStatusException(HttpStatus.CONFLICT, "Evrak sıra no zaten mevcut: " + evrakKayitDTO.getEvrakSiraNo());
            }
        }

        EvrakKayit evrakKayit = evrakKayitMapper.toEntity(evrakKayitDTO);
        dbEvrakKayitService.save(evrakKayit);
        log.info("Evrak kayıt oluşturuldu: {}", evrakKayit.getId());
        return evrakKayitMapper.toDto(evrakKayit);
    }

    /**
     * Update evrak kayit
     * @param id Evrak kayit ID
     * @param evrakKayitDTO EvrakKayitDTO
     * @return Updated EvrakKayitDTO
     */
    public EvrakKayitDTO update(Long id, EvrakKayitDTO evrakKayitDTO) {
        EvrakKayit existingEvrakKayit = dbEvrakKayitService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Evrak kayıt bulunamadı: " + id));

        // Check if evrakSiraNo is being changed and already exists
        if (evrakKayitDTO.getEvrakSiraNo() != null &&
            !evrakKayitDTO.getEvrakSiraNo().equals(existingEvrakKayit.getEvrakSiraNo())) {
            boolean evrakKayitWithSameNo = dbEvrakKayitService.existsByEvrakSiraNo(evrakKayitDTO.getEvrakSiraNo());
            if (evrakKayitWithSameNo) {
                throw new ResponseStatusException(HttpStatus.CONFLICT, "Evrak sıra no zaten mevcut: " + evrakKayitDTO.getEvrakSiraNo());
            }
        }

        EvrakKayit updatedEvrakKayit = evrakKayitMapper.updateEntityFromDto(existingEvrakKayit, evrakKayitDTO);
        dbEvrakKayitService.update(updatedEvrakKayit);
        log.info("Evrak kayıt güncellendi: {}", updatedEvrakKayit.getId());
        return evrakKayitMapper.toDto(updatedEvrakKayit);
    }

    /**
     * Delete evrak kayit
     * @param id Evrak kayit ID
     */
    public void delete(Long id) {
        EvrakKayit evrakKayit = dbEvrakKayitService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Evrak kayıt bulunamadı: " + id));

        dbEvrakKayitService.delete(evrakKayit);
        log.info("Evrak kayıt silindi: {}", id);
    }
}
