package iym.common.service.db;

import iym.common.model.entity.iym.MahkemeKararSuclar;

import java.util.List;
import java.util.Optional;

/**
 * Service interface for MahkemeKararSuc entity
 */
public interface DBMahkemeKararSucService extends GenericDbService<MahkemeKararSuclar, Long> {

    List<MahkemeKararSuclar> findByMahkemeKararId(Long mahkemeKararId);

    Optional<MahkemeKararSuclar> findByMahkemeKararIdAndSucTipKodu(Long mahkemeKararId, String sucTipKodu);

}
