/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import iym.makos.api.client.gen.model.AidiyatGuncellemeDetay;
import iym.makos.api.client.gen.model.MahkemeKararDetay;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * <PERSON><PERSON>ncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek aidiyat bilgileri
 */
@JsonPropertyOrder({
  AidiyatGuncellemeKararDetay.JSON_PROPERTY_MAHKEME_KARAR_DETAY,
  AidiyatGuncellemeKararDetay.JSON_PROPERTY_AIDIYAT_GUNCELLEME_DETAY_LISTESI
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class AidiyatGuncellemeKararDetay {
  public static final String JSON_PROPERTY_MAHKEME_KARAR_DETAY = "mahkemeKararDetay";
  private MahkemeKararDetay mahkemeKararDetay;

  public static final String JSON_PROPERTY_AIDIYAT_GUNCELLEME_DETAY_LISTESI = "aidiyatGuncellemeDetayListesi";
  private List<AidiyatGuncellemeDetay> aidiyatGuncellemeDetayListesi = new ArrayList<>();

  public AidiyatGuncellemeKararDetay() {
  }

  public AidiyatGuncellemeKararDetay mahkemeKararDetay(MahkemeKararDetay mahkemeKararDetay) {
    
    this.mahkemeKararDetay = mahkemeKararDetay;
    return this;
  }

   /**
   * Get mahkemeKararDetay
   * @return mahkemeKararDetay
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DETAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public MahkemeKararDetay getMahkemeKararDetay() {
    return mahkemeKararDetay;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DETAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMahkemeKararDetay(MahkemeKararDetay mahkemeKararDetay) {
    this.mahkemeKararDetay = mahkemeKararDetay;
  }


  public AidiyatGuncellemeKararDetay aidiyatGuncellemeDetayListesi(List<AidiyatGuncellemeDetay> aidiyatGuncellemeDetayListesi) {
    
    this.aidiyatGuncellemeDetayListesi = aidiyatGuncellemeDetayListesi;
    return this;
  }

  public AidiyatGuncellemeKararDetay addAidiyatGuncellemeDetayListesiItem(AidiyatGuncellemeDetay aidiyatGuncellemeDetayListesiItem) {
    if (this.aidiyatGuncellemeDetayListesi == null) {
      this.aidiyatGuncellemeDetayListesi = new ArrayList<>();
    }
    this.aidiyatGuncellemeDetayListesi.add(aidiyatGuncellemeDetayListesiItem);
    return this;
  }

   /**
   * Get aidiyatGuncellemeDetayListesi
   * @return aidiyatGuncellemeDetayListesi
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_AIDIYAT_GUNCELLEME_DETAY_LISTESI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public List<AidiyatGuncellemeDetay> getAidiyatGuncellemeDetayListesi() {
    return aidiyatGuncellemeDetayListesi;
  }


  @JsonProperty(JSON_PROPERTY_AIDIYAT_GUNCELLEME_DETAY_LISTESI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setAidiyatGuncellemeDetayListesi(List<AidiyatGuncellemeDetay> aidiyatGuncellemeDetayListesi) {
    this.aidiyatGuncellemeDetayListesi = aidiyatGuncellemeDetayListesi;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AidiyatGuncellemeKararDetay aidiyatGuncellemeKararDetay = (AidiyatGuncellemeKararDetay) o;
    return Objects.equals(this.mahkemeKararDetay, aidiyatGuncellemeKararDetay.mahkemeKararDetay) &&
        Objects.equals(this.aidiyatGuncellemeDetayListesi, aidiyatGuncellemeKararDetay.aidiyatGuncellemeDetayListesi);
  }

  @Override
  public int hashCode() {
    return Objects.hash(mahkemeKararDetay, aidiyatGuncellemeDetayListesi);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class AidiyatGuncellemeKararDetay {\n");
    sb.append("    mahkemeKararDetay: ").append(toIndentedString(mahkemeKararDetay)).append("\n");
    sb.append("    aidiyatGuncellemeDetayListesi: ").append(toIndentedString(aidiyatGuncellemeDetayListesi)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

