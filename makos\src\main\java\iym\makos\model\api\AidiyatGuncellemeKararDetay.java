package iym.makos.model.api;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

import java.util.List;

@Data
@Builder
@Jacksonized
@ToString
@EqualsAndHashCode
public class AidiyatGuncellemeKararDetay {

  @NotNull
  @Schema(description = "Aidiyat değişikliği yapılacak mahkeme karar bilgileri")
  private MahkemeKararDetay mahkemeKararDetay;

  @NotNull
  @Size(min = 1)
  @Valid
  private List<AidiyatGuncellemeDetay> aidiyatGuncellemeDetayListesi;

}

