package iym.makos.controller;

import iym.makos.config.security.UserDetailsImpl;
import iym.makos.dto.talepupdate.MahkemeKararTalepUpdateRequest;
import iym.makos.dto.talepupdate.MahkemeKararTalepUpdateResponse;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import iym.makos.talepislem.TalepOnayService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/mahkemeTalepOnay")
@Slf4j
public class MahkemeTalepOnayController {

    @Autowired
    private TalepOnayService talepOnayService;


    @PostMapping("baseStationInfo")
    @PreAuthorize("hasAnyRole('ADMIN', 'KURUM_TEMSILCISI')")
    public ResponseEntity<MahkemeKararTalepUpdateResponse> talepGuncelle(
            @Valid @RequestBody MahkemeKararTalepUpdateRequest request, Authentication authentication) {
        log.debug("getBaseStationInfo request received. id:{}", request.getId());

        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();
            log.info("EvrakOnayla Request received:{}, user:{}", request, user.getUsername());

            MahkemeKararTalepUpdateResponse response = talepOnayService.process(request, user.getId());
            return ResponseEntity.status(MakosResponseCode.toHttpStatus(response.getResponse().getResponseCode()))
                    .body(response);

        }catch (Exception ex){
            log.error("MahkemeKararTalepUpdateRequest process failed, requestId:{}, mahkemeKararTalepId:{}", request.getId(), request.getMahkemeKararTalepId(), ex);
            MahkemeKararTalepUpdateResponse response = MahkemeKararTalepUpdateResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

}
