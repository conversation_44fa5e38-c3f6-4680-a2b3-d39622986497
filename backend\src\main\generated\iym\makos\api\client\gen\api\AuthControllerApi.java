package iym.makos.api.client.gen.api;

import iym.makos.api.client.gen.handler.ApiClient;

import iym.makos.api.client.gen.model.ChangePasswordRequest;
import iym.makos.api.client.gen.model.ChangePasswordResponse;
import iym.makos.api.client.gen.model.LoginRequest;
import iym.makos.api.client.gen.model.LoginResponse;
import iym.makos.api.client.gen.model.RegisterRequest;
import iym.makos.api.client.gen.model.RegisterResponse;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class AuthControllerApi {
    private ApiClient apiClient;

    public AuthControllerApi() {
        this(new ApiClient());
    }

    public AuthControllerApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param changePasswordRequest  (required)
     * @return ChangePasswordResponse
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ChangePasswordResponse changePassword(ChangePasswordRequest changePasswordRequest) throws RestClientException {
        return changePasswordWithHttpInfo(changePasswordRequest).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param changePasswordRequest  (required)
     * @return ResponseEntity&lt;ChangePasswordResponse&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ChangePasswordResponse> changePasswordWithHttpInfo(ChangePasswordRequest changePasswordRequest) throws RestClientException {
        Object localVarPostBody = changePasswordRequest;
        
        // verify the required parameter 'changePasswordRequest' is set
        if (changePasswordRequest == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'changePasswordRequest' when calling changePassword");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "*/*"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "BasicAuth" };

        ParameterizedTypeReference<ChangePasswordResponse> localReturnType = new ParameterizedTypeReference<ChangePasswordResponse>() {};
        return apiClient.invokeAPI("/auth/changePassword", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param loginRequest  (required)
     * @return LoginResponse
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public LoginResponse login(LoginRequest loginRequest) throws RestClientException {
        return loginWithHttpInfo(loginRequest).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param loginRequest  (required)
     * @return ResponseEntity&lt;LoginResponse&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<LoginResponse> loginWithHttpInfo(LoginRequest loginRequest) throws RestClientException {
        Object localVarPostBody = loginRequest;
        
        // verify the required parameter 'loginRequest' is set
        if (loginRequest == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'loginRequest' when calling login");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "*/*"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "BasicAuth" };

        ParameterizedTypeReference<LoginResponse> localReturnType = new ParameterizedTypeReference<LoginResponse>() {};
        return apiClient.invokeAPI("/auth/login", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param registerRequest  (required)
     * @return RegisterResponse
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public RegisterResponse register(RegisterRequest registerRequest) throws RestClientException {
        return registerWithHttpInfo(registerRequest).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param registerRequest  (required)
     * @return ResponseEntity&lt;RegisterResponse&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<RegisterResponse> registerWithHttpInfo(RegisterRequest registerRequest) throws RestClientException {
        Object localVarPostBody = registerRequest;
        
        // verify the required parameter 'registerRequest' is set
        if (registerRequest == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'registerRequest' when calling register");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "*/*"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "BasicAuth" };

        ParameterizedTypeReference<RegisterResponse> localReturnType = new ParameterizedTypeReference<RegisterResponse>() {};
        return apiClient.invokeAPI("/auth/register", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
}
