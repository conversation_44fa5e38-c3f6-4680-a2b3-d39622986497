package iym.makos.model.api;

import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
@ToString
@EqualsAndHashCode
public class MahkemeKararGuncellemeBilgi {

    @NotNull
    private MahkemeKararGuncellemeAlan mahkemeKararGuncellemeAlan;

    private String yeniDegeri;

}
