package iym.makos.validator;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = MakosRequestConstraintValidator.class)
public @interface MakosRequestValid {
    String message() default "MakosRequestValid validation failed";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}