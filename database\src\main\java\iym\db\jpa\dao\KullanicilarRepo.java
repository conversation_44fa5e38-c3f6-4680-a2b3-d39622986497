package iym.db.jpa.dao;


import iym.common.model.entity.iym.Kullanicilar;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;


@Repository
public interface KullanicilarRepo extends JpaRepository<Kullanicilar, Long> {

    // Kullanıcı adına göre arama
    Optional<Kullanicilar> findByKullaniciAdi(String kullaniciAdi);

    // Durumu alanına göre arama
    List<Kullanicilar> findByDurumu(String durumu);

    // Tüm kullanıcıları kullanıcı adına göre sıralı getir
    List<Kullanicilar> findAllByOrderByKullaniciAdiAsc();

}
