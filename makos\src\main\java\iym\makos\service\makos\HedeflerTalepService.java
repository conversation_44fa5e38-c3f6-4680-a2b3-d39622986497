package iym.makos.service.makos;

import iym.common.model.entity.iym.HedeflerTalep;
import iym.common.service.db.DbHedeflerTalepService;
import iym.makos.dto.HedeflerTalepDTO;
import iym.makos.mapper.HedeflerTalepMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.util.Date;
import java.util.List;

/**
 * Service for HedeflerTalep operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HedeflerTalepService {

    private final DbHedeflerTalepService dbHedeflerTalepService;
    private final HedeflerTalepMapper hedeflerTalepMapper;

    /**
     * Get all hedefler talep records
     * @return List of HedeflerTalepDTO
     */
    @Transactional(readOnly = true)
    public List<HedeflerTalepDTO> findAll() {
        List<HedeflerTalep> hedeflerTalepList = dbHedeflerTalepService.findAll();
        return hedeflerTalepMapper.toDtoList(hedeflerTalepList);
    }

    /**
     * Get all hedefler talep records with pagination
     * @param pageable Pagination information
     * @return Page of HedeflerTalepDTO
     */
    @Transactional(readOnly = true)
    public Page<HedeflerTalepDTO> findAll(Pageable pageable) {
        Page<HedeflerTalep> hedeflerTalepPage = dbHedeflerTalepService.findAll(pageable);
        List<HedeflerTalepDTO> dtoList = hedeflerTalepMapper.toDtoList(hedeflerTalepPage.getContent());
        return new PageImpl<>(dtoList, pageable, hedeflerTalepPage.getTotalElements());
    }

    /**
     * Get hedefler talep by id
     * @param id HedeflerTalep id
     * @return HedeflerTalepDTO
     */
    @Transactional(readOnly = true)
    public HedeflerTalepDTO findById(Long id) {
        HedeflerTalep hedeflerTalep = dbHedeflerTalepService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Hedefler talep bulunamadı: " + id));
        return hedeflerTalepMapper.toDto(hedeflerTalep);
    }

    /**
     * Get hedefler talep by mahkeme karar id
     * @param mahkemeKararTalepId Mahkeme karar id
     * @return List of HedeflerTalepDTO
     */
    @Transactional(readOnly = true)
    public List<HedeflerTalepDTO> findByMahkemeKararTalepId(Long mahkemeKararTalepId) {
        List<HedeflerTalep> hedeflerTalepList = dbHedeflerTalepService.findByMahkemeKararTalepId(mahkemeKararTalepId);
        return hedeflerTalepMapper.toDtoList(hedeflerTalepList);
    }




    /**
     * Get hedefler talep by hedef no
     * @param hedefNo Hedef no
     * @return List of HedeflerTalepDTO
     */
    @Transactional(readOnly = true)
    public List<HedeflerTalepDTO> findByHedefNo(String hedefNo) {
        List<HedeflerTalep> hedeflerTalepList = dbHedeflerTalepService.findByHedefNo(hedefNo);
        return hedeflerTalepMapper.toDtoList(hedeflerTalepList);
    }




    /**
     * Get hedefler talep by baslama tarihi between
     * @param startDate Start date
     * @param endDate End date
     * @return List of HedeflerTalepDTO
     */
    @Transactional(readOnly = true)
    public List<HedeflerTalepDTO> findByBaslamaTarihiBetween(Date startDate, Date endDate) {
        List<HedeflerTalep> hedeflerTalepList = dbHedeflerTalepService.findByBaslamaTarihiBetween(startDate, endDate);
        return hedeflerTalepMapper.toDtoList(hedeflerTalepList);
    }

    /**
     * Get hedefler talep by kayit tarihi between
     * @param startDate Start date
     * @param endDate End date
     * @return List of HedeflerTalepDTO
     */
    @Transactional(readOnly = true)
    public List<HedeflerTalepDTO> findByKayitTarihiBetween(Date startDate, Date endDate) {
        List<HedeflerTalep> hedeflerTalepList = dbHedeflerTalepService.findByKayitTarihiBetween(startDate, endDate);
        return hedeflerTalepMapper.toDtoList(hedeflerTalepList);
    }

    /**
     * Get hedefler talep by tanimlama tarihi between
     * @param startDate Start date
     * @param endDate End date
     * @return List of HedeflerTalepDTO
     */
    @Transactional(readOnly = true)
    public List<HedeflerTalepDTO> findByTanimlamaTarihiBetween(Date startDate, Date endDate) {
        List<HedeflerTalep> hedeflerTalepList = dbHedeflerTalepService.findByTanimlamaTarihiBetween(startDate, endDate);
        return hedeflerTalepMapper.toDtoList(hedeflerTalepList);
    }

    /**
     * Get hedefler talep by kapatma tarihi between
     * @param startDate Start date
     * @param endDate End date
     * @return List of HedeflerTalepDTO
     */
    @Transactional(readOnly = true)
    public List<HedeflerTalepDTO> findByKapatmaTarihiBetween(Date startDate, Date endDate) {
        List<HedeflerTalep> hedeflerTalepList = dbHedeflerTalepService.findByKapatmaTarihiBetween(startDate, endDate);
        return hedeflerTalepMapper.toDtoList(hedeflerTalepList);
    }

    /**
     * Get hedefler talep by imha tarihi between
     * @param startDate Start date
     * @param endDate End date
     * @return List of HedeflerTalepDTO
     */
    @Transactional(readOnly = true)
    public List<HedeflerTalepDTO> findByImhaTarihiBetween(Date startDate, Date endDate) {
        List<HedeflerTalep> hedeflerTalepList = dbHedeflerTalepService.findByImhaTarihiBetween(startDate, endDate);
        return hedeflerTalepMapper.toDtoList(hedeflerTalepList);
    }




    /**
     * Create new hedefler talep
     * @param hedeflerTalepDTO HedeflerTalepDTO
     * @return Created HedeflerTalepDTO
     */
    @Transactional
    public HedeflerTalepDTO create(HedeflerTalepDTO hedeflerTalepDTO) {
        // Check if hedefler talep already exists
        if (hedeflerTalepDTO.getHedefNo() != null && 
            hedeflerTalepDTO.getMahkemeKararId() != null && 
            hedeflerTalepDTO.getHedefTipi() != null && 
            hedeflerTalepDTO.getUniqKod() != null) {
        }

        HedeflerTalep hedeflerTalep = hedeflerTalepMapper.toEntity(hedeflerTalepDTO);
        dbHedeflerTalepService.save(hedeflerTalep);
        log.info("Hedefler talep oluşturuldu: {}", hedeflerTalep.getId());
        return hedeflerTalepMapper.toDto(hedeflerTalep);
    }

    /**
     * Update existing hedefler talep
     * @param id HedeflerTalep id
     * @param hedeflerTalepDTO HedeflerTalepDTO
     * @return Updated HedeflerTalepDTO
     */
    @Transactional
    public HedeflerTalepDTO update(Long id, HedeflerTalepDTO hedeflerTalepDTO) {
        HedeflerTalep existingHedeflerTalep = dbHedeflerTalepService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Hedefler talep bulunamadı: " + id));

        // Check if updated hedefler talep would conflict with an existing one
        if (hedeflerTalepDTO.getHedefNo() != null && 
            hedeflerTalepDTO.getMahkemeKararId() != null && 
            hedeflerTalepDTO.getHedefTipi() != null && 
            hedeflerTalepDTO.getUniqKod() != null) {
            //TODO
        }

        HedeflerTalep updatedHedeflerTalep = hedeflerTalepMapper.updateEntityFromDto(existingHedeflerTalep, hedeflerTalepDTO);
        dbHedeflerTalepService.update(updatedHedeflerTalep);
        log.info("Hedefler talep güncellendi: {}", updatedHedeflerTalep.getId());
        return hedeflerTalepMapper.toDto(updatedHedeflerTalep);
    }

    /**
     * Delete hedefler talep
     * @param id HedeflerTalep id
     */
    @Transactional
    public void delete(Long id) {
        HedeflerTalep hedeflerTalep = dbHedeflerTalepService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Hedefler talep bulunamadı: " + id));
        dbHedeflerTalepService.delete(hedeflerTalep);
        log.info("Hedefler talep silindi: {}", id);
    }
}
