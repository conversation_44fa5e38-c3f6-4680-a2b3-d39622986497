package iym.backend.controller;

import iym.backend.service.MakosApiService;
import iym.makos.api.client.gen.model.HealthCheckResponse;
import iym.makos.api.client.gen.model.ModelApiResponse;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;

/**
 * Integration test to verify that /api/makos/health endpoint is accessible without authentication
 */
@SpringBootTest
@AutoConfigureMockMvc
@ActiveProfiles("test")
@TestPropertySource(properties = {"app.init-db=false"})
public class MakosControllerSecurityTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private MakosApiService makosApiService;

    @Test
    void healthEndpoint_ShouldBeAccessibleWithoutAuthentication() throws Exception {
        // Given
        HealthCheckResponse mockResponse = new HealthCheckResponse();
        ModelApiResponse apiResponse = new ModelApiResponse();
        apiResponse.setResponseCode(ModelApiResponse.ResponseCodeEnum.SUCCESS);
        apiResponse.setResponseMessage("Makos API is alive");
        mockResponse.setResponse(apiResponse);

        when(makosApiService.healthCheck()).thenReturn(mockResponse);

        // When & Then
        mockMvc.perform(get("/api/makos/health"))
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    void healthEndpoint_ShouldBeAccessibleWithoutAuthenticationEvenWithoutToken() throws Exception {
        // Given
        HealthCheckResponse mockResponse = new HealthCheckResponse();
        ModelApiResponse apiResponse = new ModelApiResponse();
        apiResponse.setResponseCode(ModelApiResponse.ResponseCodeEnum.SUCCESS);
        apiResponse.setResponseMessage("Makos API is alive");
        mockResponse.setResponse(apiResponse);

        when(makosApiService.healthCheck()).thenReturn(mockResponse);

        // When & Then - No Authorization header at all
        mockMvc.perform(get("/api/makos/health"))
                .andDo(print())
                .andExpect(status().isOk());
    }
}
