package iym.makos.model.api;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

import java.util.List;

@Data
@Builder
@Jacksonized
@ToString
@EqualsAndHashCode
public class MahkemeKararIT {

  @NotNull
  private MahkemeKararBilgisi mahkemeKararBilgisi;

  @NotNull
  @Size(min = 1)
  @Valid
  private List<ITHedefDetay> hedefDetayListesi;

}

