package iym.makos.talepislem;

import iym.common.model.api.KararTuru;
import iym.makos.dto.talepupdate.MahkemeKararTalepUpdateRequest;
import iym.makos.dto.talepupdate.MahkemeKararTalepUpdateResponse;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class GenelEvrakIsleyici extends IDMahkemeKararIsleyiciBase {

    @Override
    protected MahkemeKararTalepUpdateResponse updateRelatedTables(MahkemeKararTalepUpdateRequest request) {
        return MahkemeKararTalepUpdateResponse.builder()
                .requestId(request.getId())
                .response(MakosApiResponse.builder()
                        .responseCode(MakosResponseCode.SUCCESS)
                        .build())
                .build();
    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.GENEL_EVRAK;
    }

}

