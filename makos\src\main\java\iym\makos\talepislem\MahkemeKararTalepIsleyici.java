package iym.makos.talepislem;

import iym.common.model.api.KararTuru;
import iym.common.model.api.TalepGuncellemeTuru;
import iym.makos.dto.talepupdate.MahkemeKararTalepUpdateRequest;
import iym.makos.dto.talepupdate.MahkemeKararTalepUpdateResponse;


public interface MahkemeKararTalepIsleyici {

    String DURUM_ONAYLA = "ONAYLANDI";
    String DURUM_SILINDI = "SILINDI";

    MahkemeKararTalepUpdateResponse process(MahkemeKararTalepUpdateRequest request, Long kullaniciId);

    KararTuru getRelatedKararTuru();

    static String toDurum(TalepGuncellemeTuru talepGuncellemeTuru){
        return switch (talepGuncellemeTuru) {
            case ONAYLA -> DURUM_ONAYLA;
            case SIL -> DURUM_SILINDI;
        };
    }
}

