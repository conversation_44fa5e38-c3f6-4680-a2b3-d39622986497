package iym.makos.model.api;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
@ToString
@EqualsAndHashCode
public class MahkemeKararGuncellemeDetay2 {

  @NotNull
  @Schema(description = "Mahkeme karar bilgisi değişikliği yapılacak olan mahkeme karar bilgileri")
  private MahkemeKararDetay mahkemeKararDetay;

  private String mahkemeKodu;

  private String sorusturmaNo;

  private String mahkemeKararNo;
}

