/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * MahkemeKararGuncellemeBilgi
 */
@JsonPropertyOrder({
  MahkemeKararGuncellemeBilgi.JSON_PROPERTY_MAHKEME_KARAR_GUNCELLEME_ALAN,
  MahkemeKararGuncellemeBilgi.JSON_PROPERTY_YENI_DEGERI
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class MahkemeKararGuncellemeBilgi {
  /**
   * Gets or Sets mahkemeKararGuncellemeAlan
   */
  public enum MahkemeKararGuncellemeAlanEnum {
    MAHKEME_KODU("MAHKEME_KODU"),
    
    SORUSTURMA_NO("SORUSTURMA_NO"),
    
    MAHKEMEKARAR_NO("MAHKEMEKARAR_NO");

    private String value;

    MahkemeKararGuncellemeAlanEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static MahkemeKararGuncellemeAlanEnum fromValue(String value) {
      for (MahkemeKararGuncellemeAlanEnum b : MahkemeKararGuncellemeAlanEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_MAHKEME_KARAR_GUNCELLEME_ALAN = "mahkemeKararGuncellemeAlan";
  private MahkemeKararGuncellemeAlanEnum mahkemeKararGuncellemeAlan;

  public static final String JSON_PROPERTY_YENI_DEGERI = "yeniDegeri";
  private String yeniDegeri;

  public MahkemeKararGuncellemeBilgi() {
  }

  public MahkemeKararGuncellemeBilgi mahkemeKararGuncellemeAlan(MahkemeKararGuncellemeAlanEnum mahkemeKararGuncellemeAlan) {
    
    this.mahkemeKararGuncellemeAlan = mahkemeKararGuncellemeAlan;
    return this;
  }

   /**
   * Get mahkemeKararGuncellemeAlan
   * @return mahkemeKararGuncellemeAlan
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_GUNCELLEME_ALAN)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public MahkemeKararGuncellemeAlanEnum getMahkemeKararGuncellemeAlan() {
    return mahkemeKararGuncellemeAlan;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_GUNCELLEME_ALAN)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMahkemeKararGuncellemeAlan(MahkemeKararGuncellemeAlanEnum mahkemeKararGuncellemeAlan) {
    this.mahkemeKararGuncellemeAlan = mahkemeKararGuncellemeAlan;
  }


  public MahkemeKararGuncellemeBilgi yeniDegeri(String yeniDegeri) {
    
    this.yeniDegeri = yeniDegeri;
    return this;
  }

   /**
   * Get yeniDegeri
   * @return yeniDegeri
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_YENI_DEGERI)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getYeniDegeri() {
    return yeniDegeri;
  }


  @JsonProperty(JSON_PROPERTY_YENI_DEGERI)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setYeniDegeri(String yeniDegeri) {
    this.yeniDegeri = yeniDegeri;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MahkemeKararGuncellemeBilgi mahkemeKararGuncellemeBilgi = (MahkemeKararGuncellemeBilgi) o;
    return Objects.equals(this.mahkemeKararGuncellemeAlan, mahkemeKararGuncellemeBilgi.mahkemeKararGuncellemeAlan) &&
        Objects.equals(this.yeniDegeri, mahkemeKararGuncellemeBilgi.yeniDegeri);
  }

  @Override
  public int hashCode() {
    return Objects.hash(mahkemeKararGuncellemeAlan, yeniDegeri);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MahkemeKararGuncellemeBilgi {\n");
    sb.append("    mahkemeKararGuncellemeAlan: ").append(toIndentedString(mahkemeKararGuncellemeAlan)).append("\n");
    sb.append("    yeniDegeri: ").append(toIndentedString(yeniDegeri)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

