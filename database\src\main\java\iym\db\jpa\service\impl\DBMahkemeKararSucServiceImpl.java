package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.MahkemeKararSuclar;
import iym.common.service.db.DBMahkemeKararSucService;
import iym.db.jpa.dao.MahkemeKararSucTipiRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * Service implementation for MahkemeKararSuc entity
 */
@Service
public class DBMahkemeKararSucServiceImpl extends GenericDbServiceImpl<MahkemeKararSuclar, Long> implements DBMahkemeKararSucService {

    private final MahkemeKararSucTipiRepo mahkemeKararSucTipiRepo;

    @Autowired
    public DBMahkemeKararSucServiceImpl(MahkemeKararSucTipiRepo repository) {
        super(repository);
        this.mahkemeKararSucTipiRepo = repository;
    }

    @Override
    public List<MahkemeKararSuclar> findByMahkemeKararId(Long mahkemeKararId){
        return mahkemeKararSucTipiRepo.findByMahkemeKararId(mahkemeKararId);
    }

    @Override
    public Optional<MahkemeKararSuclar> findByMahkemeKararIdAndSucTipKodu(Long mahkemeKararId, String sucTipKodu){
        return mahkemeKararSucTipiRepo.findByMahkemeKararIdAndSucTipKodu(mahkemeKararId, sucTipKodu);
    }

}
