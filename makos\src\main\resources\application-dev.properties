# Development Environment Configuration for Makos
# Oracle Database configuration for development
spring.datasource.url=***********************************
spring.datasource.driverClassName=oracle.jdbc.OracleDriver
spring.datasource.username=iym
spring.datasource.password=iym
spring.jpa.database-platform=org.hibernate.dialect.OracleDialect
spring.jpa.properties.hibernate.default_schema=iym

# JPA configuration for development
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.properties.hibernate.hbm2ddl.auto=validate

# Development specific logging
logging.level.root=INFO
logging.level.iym=DEBUG
logging.level.org.hibernate=ERROR
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Connection pool configuration for development
spring.datasource.hikari.connectionTimeout=20000
spring.datasource.hikari.maximumPoolSize=5

# Application specific properties for development
# Note: <PERSON><PERSON> uses Oracle database with SQL-based seed data, no Java DataInitializer needed

# CORS Configuration for development (specific local origins)
cors.allowed.origins=http://localhost:3000,http://localhost:4000,http://127.0.0.1:3000,http://127.0.0.1:4000,http://localhost:8080
