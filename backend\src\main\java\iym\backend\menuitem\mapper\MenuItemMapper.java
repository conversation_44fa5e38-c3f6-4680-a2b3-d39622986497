package iym.backend.postgresql.menuitem.mapper;

import org.mapstruct.*;
import iym.backend.postgresql.menuitem.dto.MenuItemDto;
import iym.backend.postgresql.menuitem.entity.MenuItem;
import iym.backend.postgresql.shared.mapper.BaseMapper;

@Mapper(componentModel = "spring")
public interface MenuItemMapper extends BaseMapper<MenuItem, MenuItemDto> {
    @Mapping(target = "yetkiIds", expression = "java(menuItem.getMenuItemYetkiler() != null ? menuItem.getMenuItemYetkiler().stream().map(y -> y.getYetki().getId()).toList() : java.util.Collections.emptyList())")
    @Mapping(target = "yetkiAdlari", expression = "java(menuItem.getMenuItemYetkiler() != null ? menuItem.getMenuItemYetkiler().stream().map(y -> y.getYetki().getAd()).toList() : java.util.Collections.emptyList())")
    @Mapping(target = "parentId", expression = "java(menuItem.getParent() != null ? menuItem.getParent().getId() : null)")
    MenuItemDto toDto(MenuItem menuItem);

    @InheritInverseConfiguration
    MenuItem toEntity(MenuItemDto dto);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void updateEntityFromDto(MenuItemDto dto, @MappingTarget MenuItem entity);

}
