package iym.backend.postgresql.ulke.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import iym.backend.postgresql.shared.service.BaseService;
import iym.backend.postgresql.ulke.dto.UlkeDto;
import iym.backend.postgresql.yetki.dto.YetkiDto;

public interface UlkeService  extends BaseService<UlkeDto, Long> {
    Page<UlkeDto> getPaged(Pageable pageable);
}
