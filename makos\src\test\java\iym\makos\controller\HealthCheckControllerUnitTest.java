package iym.makos.controller;

import iym.common.model.dto.Response;
import iym.common.model.enums.ResponseCode;
import iym.common.model.enums.ResultCode;
import iym.makos.dto.healthcheck.HealthCheckResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;


import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
public class HealthCheckControllerUnitTest {

    //@Mock
    //private HelloService helloService;

    @InjectMocks
    private HealthCheckController healthCheckController;

    @BeforeEach
    void setMockOutput() {
        //when(helloService.getWelcomeMessage()).thenReturn(HealthCheckController.API_ALIVE_MESSAGE);
    }

    @Test
    public void shouldReturnDefaultMessage() {
        // When
        ResponseEntity<HealthCheckResponse> response = healthCheckController.healthCheck();

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getResponse()).isNotNull();
        assertThat(response.getBody().getResponse().getResponseCode()).isEqualTo(ResponseCode.SUCCESS);
        assertThat(response.getBody().getResponse().getResponseMessage()).isEqualTo(HealthCheckController.API_ALIVE_MESSAGE);
    }
}
