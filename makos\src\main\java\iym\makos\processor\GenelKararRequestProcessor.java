package iym.makos.processor;

import iym.common.validation.ValidationResult;
import iym.makos.config.security.UserDetailsImpl;
import iym.makos.dto.id.GenelEvrakRequest;
import iym.makos.dto.it.GenelKararResponse;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
@Slf4j
public class GenelKararRequestProcessor extends MakosRequestProcessorBase<GenelEvrakRequest, GenelKararResponse> {

    @Override
    public GenelKararResponse process(GenelEvrakRequest request, UserDetailsImpl islemYapanKullanici) {
        try {
            ValidationResult validationResult = requestValidator.validate(request);
            if (!validationResult.isValid()) {
                return GenelKararResponse.builder()
                        .response(MakosApiResponse.builder()
                                .responseCode(MakosResponseCode.INVALID_REQUEST)
                                .responseMessage(validationResult.getReasons().toString())
                                .build())
                        .build();
            }

            Date kayitTarihi = new Date();
            Long kaydedenKullaniciId = islemYapanKullanici.getId();
            Long mahkemeKararTalepId = requestSaver.kaydet(request, kayitTarihi, kaydedenKullaniciId);

            return GenelKararResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .responseMessage(validationResult.getReasons().toString())
                            .build())
                    .evrakId(mahkemeKararTalepId)
                    .build();

        } catch (Exception ex) {
            log.error("GenelKararRequest process failed. id:{}, evrakNo:{}", request.getId(), request.getEvrakDetay().getEvrakNo(), ex);
            return GenelKararResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("INTERNAL ERROR")
                            .build())
                    .build();
        }
    }
}
