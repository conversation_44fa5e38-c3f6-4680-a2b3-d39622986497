package iym.common.model.api;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum KararTuru {
	ILETISIMIN_DENETLENMESI_YENI_KARAR(0),
	ILETISIMIN_DENETLENMESI_UZATMA_KARARI(1),
	ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI(2),
	ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME(3),
	ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME(4),
	ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME(5),
	ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME(6),
	ILETISIMIN_TESPITI(7),
	GENEL_EVRAK(8),
	ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME(9),
	;


	private final int kararTuru;

	KararTuru(int kararTuru){
		this.kararTuru = kararTuru;
	}

	@JsonValue
	public int getKararTuru(){
		return this.kararTuru;
	}

	@JsonCreator
	public static KararTuru fromName(String name) {
		for (KararTuru kararTuru : KararTuru.values()) {
			if (kararTuru.name().equals(name)) {
				return kararTuru;
			}
		}
		throw new IllegalArgumentException("Gecersiz kararTuru: '" + name + "'");
	}

	//@JsonCreator
	public static KararTuru fromValue(int value) {
		for (KararTuru evrakTuru : KararTuru.values()) {
			if (evrakTuru.kararTuru == value) {
				return evrakTuru;
			}
		}
		throw new IllegalArgumentException("Gecersiz kararTuru: '" + value + "'");
	}
}
