package iym.makos.validator;

import iym.common.validation.ValidationResult;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.extern.slf4j.Slf4j;
import iym.makos.model.MakosRequest;

@Slf4j
public class MakosRequestConstraintValidator implements ConstraintValidator<MakosRequestValid, MakosRequest> {

    @Override
    public void initialize(MakosRequestValid constraintAnnotation) {
        // Initialization logic (if needed)
    }

    @Override
    public boolean isValid(MakosRequest request, ConstraintValidatorContext context) {
        log.trace("Checking if MakosRequest is valid");

        ValidationResult validationResult = request.isValid();

        if (!validationResult.isValid()) {
            context.disableDefaultConstraintViolation();
            String joined = String.join(",", validationResult.getReasons());
            context.buildConstraintViolationWithTemplate(joined).addConstraintViolation();
            log.error("MakosRequest is not valid:{}", joined);
        }

        return validationResult.isValid();
    }
}
