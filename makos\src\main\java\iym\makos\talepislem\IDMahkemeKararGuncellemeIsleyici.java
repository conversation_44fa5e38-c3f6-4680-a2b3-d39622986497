package iym.makos.talepislem;

import iym.common.model.api.KararTuru;
import iym.common.model.entity.iym.MahkemeKararBilgiGuncellemeTalep;
import iym.common.util.CommonUtils;
import iym.db.jpa.dao.DetayMahkemeKararTalepRepo;
import iym.db.jpa.dao.MahkemeKararBilgiGuncelleTalepRepo;
import iym.makos.dto.talepupdate.MahkemeKararTalepUpdateRequest;
import iym.makos.dto.talepupdate.MahkemeKararTalepUpdateResponse;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@Slf4j
public class IDMahkemeKararGuncellemeIsleyici extends IDMahkemeKararIsleyiciBase {

    private final DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo;
    private final MahkemeKararBilgiGuncelleTalepRepo mahkemeKararBilgiGuncelleTalepRepo;

    @Autowired
    public IDMahkemeKararGuncellemeIsleyici(MahkemeKararBilgiGuncelleTalepRepo mahkemeKararBilgiGuncelleTalepRepo
            , DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo

    ) {
        this.mahkemeKararBilgiGuncelleTalepRepo = mahkemeKararBilgiGuncelleTalepRepo;
        this.dMahkemeKararTalepRepo = dMahkemeKararTalepRepo;
    }

    protected MahkemeKararTalepUpdateResponse updateRelatedTables(MahkemeKararTalepUpdateRequest request) {

        try {
            String durum = MahkemeKararTalepIsleyici.toDurum(request.getTalepGuncellemeTuru());
            CommonUtils.safeList(dMahkemeKararTalepRepo.findByMahkemeKararTalepId(request.getMahkemeKararTalepId()))
                    .forEach(dMahkemeKararTalep -> {
                        dMahkemeKararTalep.setDurum(durum);
                        dMahkemeKararTalepRepo.save(dMahkemeKararTalep);

                        Optional<MahkemeKararBilgiGuncellemeTalep> mahkemeKararBilgiGuncelleTalepOpt = mahkemeKararBilgiGuncelleTalepRepo.findByMahkemeKararDetayId(dMahkemeKararTalep.getMahkemeKararTalepId());
                        if(mahkemeKararBilgiGuncelleTalepOpt.isPresent()){
                            MahkemeKararBilgiGuncellemeTalep mahkemeKararBilgiGuncellemeTalep = mahkemeKararBilgiGuncelleTalepOpt.get();
                            mahkemeKararBilgiGuncellemeTalep.setDurumu(durum);
                            mahkemeKararBilgiGuncelleTalepRepo.save(mahkemeKararBilgiGuncellemeTalep);
                        }

                    });

            return MahkemeKararTalepUpdateResponse.builder()
                    .requestId(request.getId())
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .build())
                    .build();

        }catch (Exception ex){
            log.error("MahkemeKararTalepUpdateRequest process failed, requestId:{}, mahkemeKararTalepId:{}", request.getId(), request.getMahkemeKararTalepId(), ex);
            return MahkemeKararTalepUpdateResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Iliskili veritabanı guncelleme hatası")
                            .build())
                    .build();
        }
    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME;
    }

}

