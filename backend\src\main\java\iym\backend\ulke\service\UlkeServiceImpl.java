package iym.backend.ulke.service.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import iym.backend.shared.service.BaseServiceImpl;
import iym.backend.ulke.dto.UlkeDto;
import iym.backend.ulke.entity.Ulke;
import iym.backend.ulke.mapper.UlkeMapper;
import iym.backend.ulke.repository.UlkeRepository;
import iym.backend.ulke.service.UlkeService;
import iym.backend.yetki.dto.YetkiDto;
import iym.backend.yetki.entity.Yetki;
import iym.backend.yetki.mapper.YetkiMapper;
import iym.backend.yetki.repository.YetkiRepository;

@Service

public class UlkeServiceImpl extends BaseServiceImpl<Ulke, UlkeDto, Long> implements UlkeService {

    private final UlkeRepository ulkeRepository;
    private final UlkeMapper ulkeMapper;

    public UlkeServiceImpl(UlkeRepository repository, UlkeMapper mapper) {
        super(repository, mapper);
        this.ulkeRepository = repository;
        this.ulkeMapper = mapper;
    }
    @Override
    public Page<UlkeDto> getPaged(Pageable pageable) {
        return ulkeRepository.findAll(pageable)
                .map(ulkeMapper::toDto);
    }


}

