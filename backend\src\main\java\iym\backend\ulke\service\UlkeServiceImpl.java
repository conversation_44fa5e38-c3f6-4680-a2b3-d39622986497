package iym.backend.postgresql.ulke.service.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import iym.backend.postgresql.shared.service.BaseServiceImpl;
import iym.backend.postgresql.ulke.dto.UlkeDto;
import iym.backend.postgresql.ulke.entity.Ulke;
import iym.backend.postgresql.ulke.mapper.UlkeMapper;
import iym.backend.postgresql.ulke.repository.UlkeRepository;
import iym.backend.postgresql.ulke.service.UlkeService;
import iym.backend.postgresql.yetki.dto.YetkiDto;
import iym.backend.postgresql.yetki.entity.Yetki;
import iym.backend.postgresql.yetki.mapper.YetkiMapper;
import iym.backend.postgresql.yetki.repository.YetkiRepository;

@Service

public class UlkeServiceImpl extends BaseServiceImpl<Ulke, UlkeDto, Long> implements UlkeService {

    private final UlkeRepository ulkeRepository;
    private final UlkeMapper ulkeMapper;

    public UlkeServiceImpl(UlkeRepository repository, UlkeMapper mapper) {
        super(repository, mapper);
        this.ulkeRepository = repository;
        this.ulkeMapper = mapper;
    }
    @Override
    public Page<UlkeDto> getPaged(Pageable pageable) {
        return ulkeRepository.findAll(pageable)
                .map(ulkeMapper::toDto);
    }


}
