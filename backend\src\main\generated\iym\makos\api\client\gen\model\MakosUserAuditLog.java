/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDateTime;
import java.util.UUID;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * MakosUserAuditLog
 */
@JsonPropertyOrder({
  MakosUserAuditLog.JSON_PROPERTY_ID,
  MakosUserAuditLog.JSON_PROPERTY_USER_AUDIT_TYPE,
  MakosUserAuditLog.JSON_PROPERTY_USERNAME,
  MakosUserAuditLog.JSON_PROPERTY_ACTING_USERNAME,
  MakosUserAuditLog.JSON_PROPERTY_USER_IP,
  MakosUserAuditLog.JSON_PROPERTY_ADMIN_OPERATED_USERNAME,
  MakosUserAuditLog.JSON_PROPERTY_REQUEST_TIME,
  MakosUserAuditLog.JSON_PROPERTY_RESPONSE_TIME,
  MakosUserAuditLog.JSON_PROPERTY_RESPONSE_CODE
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class MakosUserAuditLog {
  public static final String JSON_PROPERTY_ID = "id";
  private UUID id;

  /**
   * Gets or Sets userAuditType
   */
  public enum UserAuditTypeEnum {
    LOGIN("LOGIN"),
    
    LOGOUT("LOGOUT"),
    
    IMPERSONATE_LOGIN("IMPERSONATE_LOGIN"),
    
    CHANGE_PASSWORD("CHANGE_PASSWORD"),
    
    GET_USERS_FOR_ADMIN("GET_USERS_FOR_ADMIN"),
    
    ACTIVATE("ACTIVATE"),
    
    DEACTIVATE("DEACTIVATE"),
    
    ADD("ADD"),
    
    UPDATE("UPDATE"),
    
    DELETE("DELETE"),
    
    REGISTER("REGISTER");

    private String value;

    UserAuditTypeEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static UserAuditTypeEnum fromValue(String value) {
      for (UserAuditTypeEnum b : UserAuditTypeEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_USER_AUDIT_TYPE = "userAuditType";
  private UserAuditTypeEnum userAuditType;

  public static final String JSON_PROPERTY_USERNAME = "username";
  private String username;

  public static final String JSON_PROPERTY_ACTING_USERNAME = "actingUsername";
  private String actingUsername;

  public static final String JSON_PROPERTY_USER_IP = "userIp";
  private String userIp;

  public static final String JSON_PROPERTY_ADMIN_OPERATED_USERNAME = "adminOperatedUsername";
  private String adminOperatedUsername;

  public static final String JSON_PROPERTY_REQUEST_TIME = "requestTime";
  private LocalDateTime requestTime;

  public static final String JSON_PROPERTY_RESPONSE_TIME = "responseTime";
  private LocalDateTime responseTime;

  public static final String JSON_PROPERTY_RESPONSE_CODE = "responseCode";
  private Integer responseCode;

  public MakosUserAuditLog() {
  }

  public MakosUserAuditLog id(UUID id) {
    
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public UUID getId() {
    return id;
  }


  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setId(UUID id) {
    this.id = id;
  }


  public MakosUserAuditLog userAuditType(UserAuditTypeEnum userAuditType) {
    
    this.userAuditType = userAuditType;
    return this;
  }

   /**
   * Get userAuditType
   * @return userAuditType
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_USER_AUDIT_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public UserAuditTypeEnum getUserAuditType() {
    return userAuditType;
  }


  @JsonProperty(JSON_PROPERTY_USER_AUDIT_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUserAuditType(UserAuditTypeEnum userAuditType) {
    this.userAuditType = userAuditType;
  }


  public MakosUserAuditLog username(String username) {
    
    this.username = username;
    return this;
  }

   /**
   * Get username
   * @return username
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_USERNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getUsername() {
    return username;
  }


  @JsonProperty(JSON_PROPERTY_USERNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUsername(String username) {
    this.username = username;
  }


  public MakosUserAuditLog actingUsername(String actingUsername) {
    
    this.actingUsername = actingUsername;
    return this;
  }

   /**
   * Get actingUsername
   * @return actingUsername
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ACTING_USERNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getActingUsername() {
    return actingUsername;
  }


  @JsonProperty(JSON_PROPERTY_ACTING_USERNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setActingUsername(String actingUsername) {
    this.actingUsername = actingUsername;
  }


  public MakosUserAuditLog userIp(String userIp) {
    
    this.userIp = userIp;
    return this;
  }

   /**
   * Get userIp
   * @return userIp
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_USER_IP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getUserIp() {
    return userIp;
  }


  @JsonProperty(JSON_PROPERTY_USER_IP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUserIp(String userIp) {
    this.userIp = userIp;
  }


  public MakosUserAuditLog adminOperatedUsername(String adminOperatedUsername) {
    
    this.adminOperatedUsername = adminOperatedUsername;
    return this;
  }

   /**
   * Get adminOperatedUsername
   * @return adminOperatedUsername
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ADMIN_OPERATED_USERNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAdminOperatedUsername() {
    return adminOperatedUsername;
  }


  @JsonProperty(JSON_PROPERTY_ADMIN_OPERATED_USERNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAdminOperatedUsername(String adminOperatedUsername) {
    this.adminOperatedUsername = adminOperatedUsername;
  }


  public MakosUserAuditLog requestTime(LocalDateTime requestTime) {
    
    this.requestTime = requestTime;
    return this;
  }

   /**
   * Get requestTime
   * @return requestTime
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REQUEST_TIME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDateTime getRequestTime() {
    return requestTime;
  }


  @JsonProperty(JSON_PROPERTY_REQUEST_TIME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRequestTime(LocalDateTime requestTime) {
    this.requestTime = requestTime;
  }


  public MakosUserAuditLog responseTime(LocalDateTime responseTime) {
    
    this.responseTime = responseTime;
    return this;
  }

   /**
   * Get responseTime
   * @return responseTime
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RESPONSE_TIME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDateTime getResponseTime() {
    return responseTime;
  }


  @JsonProperty(JSON_PROPERTY_RESPONSE_TIME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setResponseTime(LocalDateTime responseTime) {
    this.responseTime = responseTime;
  }


  public MakosUserAuditLog responseCode(Integer responseCode) {
    
    this.responseCode = responseCode;
    return this;
  }

   /**
   * Get responseCode
   * @return responseCode
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RESPONSE_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getResponseCode() {
    return responseCode;
  }


  @JsonProperty(JSON_PROPERTY_RESPONSE_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setResponseCode(Integer responseCode) {
    this.responseCode = responseCode;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MakosUserAuditLog makosUserAuditLog = (MakosUserAuditLog) o;
    return Objects.equals(this.id, makosUserAuditLog.id) &&
        Objects.equals(this.userAuditType, makosUserAuditLog.userAuditType) &&
        Objects.equals(this.username, makosUserAuditLog.username) &&
        Objects.equals(this.actingUsername, makosUserAuditLog.actingUsername) &&
        Objects.equals(this.userIp, makosUserAuditLog.userIp) &&
        Objects.equals(this.adminOperatedUsername, makosUserAuditLog.adminOperatedUsername) &&
        Objects.equals(this.requestTime, makosUserAuditLog.requestTime) &&
        Objects.equals(this.responseTime, makosUserAuditLog.responseTime) &&
        Objects.equals(this.responseCode, makosUserAuditLog.responseCode);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, userAuditType, username, actingUsername, userIp, adminOperatedUsername, requestTime, responseTime, responseCode);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MakosUserAuditLog {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    userAuditType: ").append(toIndentedString(userAuditType)).append("\n");
    sb.append("    username: ").append(toIndentedString(username)).append("\n");
    sb.append("    actingUsername: ").append(toIndentedString(actingUsername)).append("\n");
    sb.append("    userIp: ").append(toIndentedString(userIp)).append("\n");
    sb.append("    adminOperatedUsername: ").append(toIndentedString(adminOperatedUsername)).append("\n");
    sb.append("    requestTime: ").append(toIndentedString(requestTime)).append("\n");
    sb.append("    responseTime: ").append(toIndentedString(responseTime)).append("\n");
    sb.append("    responseCode: ").append(toIndentedString(responseCode)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

