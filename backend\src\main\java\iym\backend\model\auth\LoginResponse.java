package iym.backend.model.auth;

import iym.common.model.api.KullaniciKurum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;
import java.util.UUID;

/**
 * Login response model containing JWT token and user information
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LoginResponse {

    private String token;
    private UUID userId;
    private String username;
    private String actingUserName;
    private Set<String> roles;
    private KullaniciKurum kurum;
}

